# 任务6实现文档：测试执行引擎

## 概述

本文档描述了任务6的实现，包括MCP协议客户端和测试结果生成器的开发。

## 实现的功能

### 6.1 MCP协议客户端 (`src/managers/mcpClient.ts`)

#### 核心功能
- **MCP连接管理**: 与Playwright MCP服务建立和管理连接
- **YAML步骤解析**: 解析测试步骤文档格式
- **AI驱动测试执行**: 使用专业测试人员提示词模板，让AI调用MCP工具
- **流式交互**: 支持实时进度反馈和状态更新
- **错误处理**: 完善的错误捕获和日志记录
- **日志生成**: 自动生成详细的执行日志文件

#### 主要接口
```typescript
export interface TestStepsDocument {
    name: string;           // 测试名称
    description: string;    // 测试描述
    target: string;         // 目标网站URL
    steps: TestStep[];      // 测试步骤数组
}

export interface TestProgress {
    testName: string;       // 测试名称
    currentStep: number;    // 当前步骤索引
    totalSteps: number;     // 总步骤数
    stepDescription: string; // 当前步骤描述
    status: 'running' | 'completed' | 'failed' | 'cancelled';
}
```

#### 使用示例
```typescript
const mcpClient = MCPClient.getInstance();
await mcpClient.connect();

// 设置进度回调
mcpClient.onProgress((progress) => {
    console.log(`${progress.testName}: ${progress.stepDescription}`);
});

// 执行测试
const result = await mcpClient.executeTest('path/to/test.yaml');
```

### 6.3 测试结果生成器 (`src/managers/resultGenerator.ts`)

#### 核心功能
- **Markdown报告生成**: 生成详细的测试结果报告
- **多种报告格式**: 支持单个测试报告和汇总报告
- **截图管理**: 自动收集和展示测试截图
- **错误报告**: 生成专门的错误分析报告
- **报告配置**: 灵活的报告内容配置选项

#### 报告类型
1. **单个测试报告** (`*.result.md`)
   - 测试基本信息
   - 步骤执行统计
   - 详细步骤结果
   - 截图汇总

2. **汇总报告** (`test-summary.*.md`)
   - 整体执行统计
   - 测试结果概览表格
   - 详细测试结果

3. **错误报告** (`*.error.md`)
   - 主要错误信息
   - 步骤错误详情
   - 错误堆栈信息

#### 使用示例
```typescript
const generator = TestResultGenerator.getInstance();

// 生成单个测试报告
await generator.saveTestReport(result, outputPath, {
    includeScreenshots: true,
    includeErrorDetails: true
});

// 生成汇总报告
await generator.saveSummaryReport(results, outputPath);
```

### 测试执行器 (`src/managers/testExecutor.ts`)

#### 核心功能
- **统一执行接口**: 整合MCP客户端和结果生成器
- **批量测试执行**: 支持执行多个测试
- **事件驱动**: 提供丰富的执行事件回调
- **配置验证**: 自动验证AI和MCP配置
- **资源管理**: 自动管理连接和清理资源

#### 执行选项
```typescript
export interface TestExecutionOptions {
    generateReport: boolean;        // 是否生成报告
    generateErrorReport: boolean;   // 是否生成错误报告
    reportConfig?: Partial<ReportConfig>; // 报告配置
    outputDirectory?: string;       // 输出目录
}
```

## 集成到VS Code扩展

### 命令更新
- `ai-test.executeTests`: 执行多个测试，支持进度显示和取消
- `ai-test.executeTest`: 执行单个测试，支持实时反馈

### 进度显示
- 使用VS Code的进度API显示执行状态
- 支持取消正在执行的测试
- 实时显示当前步骤和进度

### 错误处理
- 友好的错误消息显示
- 自动生成错误报告
- 提供查看日志的选项

## 文件结构

```
src/managers/
├── mcpClient.ts        # MCP协议客户端
├── resultGenerator.ts  # 测试结果生成器
└── testExecutor.ts     # 测试执行器

test-results/           # 测试结果输出目录
├── *.result.md        # 测试结果报告
├── *.error.md         # 错误报告
├── *.log.md           # 执行日志
└── test-summary.*.md  # 汇总报告
```

## 配置要求

### AI服务配置
- API地址 (`ai-test.aiService.apiUrl`)
- API密钥 (`ai-test.aiService.apiKey`)
- 模型名称 (`ai-test.aiService.model`)

### 测试目录配置
- 测试目录路径 (`ai-test.testDirectory`)

### MCP服务要求
- 需要安装Playwright MCP服务: `@playwright/mcp@latest`
- 支持headless模式执行

## 使用流程

1. **配置环境**
   - 配置AI服务连接信息
   - 设置测试文档目录

2. **准备测试文档**
   - 创建测试描述文档 (*.md)
   - 生成测试步骤文档 (*.yaml)

3. **执行测试**
   - 选择要执行的测试
   - 监控执行进度
   - 查看执行结果

4. **查看报告**
   - 自动生成的结果报告
   - 错误分析报告
   - 执行日志文件

## 技术特点

### AI驱动执行
- 使用专业测试人员提示词模板
- AI自主选择和调用Playwright MCP工具
- 智能错误处理和重试机制

### 流式交互
- 实时进度更新
- 支持执行取消
- 详细的状态反馈

### 完善的日志记录
- 详细的执行日志 (*.log.md)
- 结构化的错误信息
- 时间戳和性能统计

### 灵活的报告系统
- 可配置的报告内容
- 多种输出格式
- 自动截图收集

## 下一步计划

1. **增强AI交互**: 实现更智能的工具选择和参数生成
2. **并行执行**: 支持多个测试的并行执行
3. **结果缓存**: 实现测试结果的缓存和去重
4. **性能优化**: 优化大量测试的执行性能
5. **UI增强**: 改进用户界面的交互体验
