
## ℹ️ INFO - 插件关闭

**时间**: 2025/7/21 19:36:34

**消息**: AI 测试插件已关闭

**详细信息**:

```json
{
  "timestamp": "2025-07-21T11:36:34.428Z",
  "uptime": 1039.275061708
}
```

---


## ℹ️ INFO - 插件启动

**时间**: 2025/7/21 19:36:44

**消息**: AI 测试插件已启动

**详细信息**:

```json
{
  "version": "0.0.1",
  "timestamp": "2025-07-21T11:36:44.346Z",
  "config": {
    "extensionPath": "/Users/<USER>/.cursor/extensions/ai-test.ai-test-0.0.1",
    "workspaceRoot": "/Users/<USER>/projects/auto-test",
    "nodeVersion": "v20.19.0",
    "platform": "darwin"
  },
  "workspaceRoot": "/Users/<USER>/projects/auto-test",
  "logFilePath": "/Users/<USER>/projects/auto-test/error.md"
}
```

---


## ℹ️ INFO - 插件关闭

**时间**: 2025/7/21 19:36:47

**消息**: AI 测试插件已关闭

**详细信息**:

```json
{
  "timestamp": "2025-07-21T11:36:47.510Z",
  "uptime": 12.858654167
}
```

---


## ℹ️ INFO - 插件启动

**时间**: 2025/7/21 19:36:50

**消息**: AI 测试插件已启动

**详细信息**:

```json
{
  "version": "0.0.1",
  "timestamp": "2025-07-21T11:36:50.300Z",
  "config": {
    "extensionPath": "/Users/<USER>/.cursor/extensions/ai-test.ai-test-0.0.1",
    "workspaceRoot": "/Users/<USER>/projects/auto-test",
    "nodeVersion": "v20.19.0",
    "platform": "darwin"
  },
  "workspaceRoot": "/Users/<USER>/projects/auto-test",
  "logFilePath": "/Users/<USER>/projects/auto-test/error.md"
}
```

---


## ℹ️ INFO - 插件命令

**时间**: 2025/7/21 19:36:54

**消息**: 开始执行测试

**详细信息**:

```json
{
  "testNames": [
    "search-test"
  ],
  "testCount": 1
}
```

---


## 🔍 DEBUG - 插件命令

**时间**: 2025/7/21 19:36:54

**消息**: 开始执行单个测试

**详细信息**:

```json
{
  "testFile": "/Users/<USER>/projects/auto-test/test-docs/search-test.yaml",
  "testName": "search-test"
}
```

---


## ℹ️ INFO - AI服务

**时间**: 2025/7/21 19:36:54

**消息**: AI服务已配置: https://api-inference.modelscope.cn/v1, 模型: Qwen/Qwen3-32B

---


## ℹ️ INFO - 测试执行器

**时间**: 2025/7/21 19:36:54

**消息**: AI服务配置成功

**详细信息**:

```json
{
  "apiUrl": "https://api-inference.modelscope.cn/v1",
  "model": "Qwen/Qwen3-32B"
}
```

---


## ℹ️ INFO - 测试执行器

**时间**: 2025/7/21 19:36:54

**消息**: 开始初始化MCP连接

**详细信息**:

```json
{
  "hasCustomConfig": false,
  "currentConnectionStatus": false
}
```

---


## 🔍 DEBUG - 测试执行器

**时间**: 2025/7/21 19:36:54

**消息**: 使用MCP配置

**详细信息**:

```json
{
  "connectionType": "stdio",
  "serverCommand": "npx",
  "timeout": 30000
}
```

---


## ℹ️ INFO - MCP客户端

**时间**: 2025/7/21 19:36:54

**消息**: 开始连接MCP服务器

**详细信息**:

```json
{
  "connectionType": "stdio",
  "serverCommand": "npx",
  "serverArgs": [
    "@playwright/mcp@latest",
    "--vision"
  ]
}
```

---


## 🔍 DEBUG - MCP客户端

**时间**: 2025/7/21 19:36:54

**消息**: 使用stdio连接类型创建AI SDK MCP客户端

---


## ℹ️ INFO - MCP客户端

**时间**: 2025/7/21 19:36:54

**消息**: 正在建立AI SDK STDIO连接

**详细信息**:

```json
{
  "command": "npx",
  "args": [
    "@playwright/mcp@latest",
    "--vision"
  ]
}
```

---


## ℹ️ INFO - MCP客户端

**时间**: 2025/7/21 19:36:57

**消息**: AI SDK STDIO连接建立成功

---


## 🔍 DEBUG - MCP客户端

**时间**: 2025/7/21 19:36:57

**消息**: 正在获取可用工具列表

---


## ℹ️ INFO - MCP客户端

**时间**: 2025/7/21 19:36:57

**消息**: MCP客户端连接成功

**详细信息**:

```json
{
  "connectionType": "stdio",
  "availableToolsCount": 27,
  "tools": [
    "browser_close",
    "browser_resize",
    "browser_console_messages",
    "browser_handle_dialog",
    "browser_evaluate",
    "browser_file_upload",
    "browser_install",
    "browser_press_key",
    "browser_type",
    "browser_navigate"
  ]
}
```

---


## ℹ️ INFO - 测试执行器

**时间**: 2025/7/21 19:36:57

**消息**: MCP连接初始化成功

---


## ℹ️ INFO - 测试执行器

**时间**: 2025/7/21 19:36:57

**消息**: 获取到AI SDK工具

**详细信息**:

```json
{
  "toolsCount": 27,
  "toolNames": [
    "browser_close",
    "browser_resize",
    "browser_console_messages",
    "browser_handle_dialog",
    "browser_evaluate",
    "browser_file_upload",
    "browser_install",
    "browser_press_key",
    "browser_type",
    "browser_navigate"
  ]
}
```

---


## ℹ️ INFO - 测试执行器

**时间**: 2025/7/21 19:36:57

**消息**: 发送AI请求

**详细信息**:

```json
{
  "testName": "search-test",
  "stepsCount": 4,
  "promptLength": 388,
  "systemPromptLength": 608
}
```

---


## ℹ️ INFO - AI服务

**时间**: 2025/7/21 19:36:57

**消息**: 发送带工具的AI流式请求

**详细信息**:

```json
{
  "model": "Qwen/Qwen3-32B",
  "messagesCount": 2,
  "toolsCount": 27,
  "contentLength": 388,
  "apiUrl": "https://api-inference.modelscope.cn/v1"
}
```

---


## ℹ️ INFO - 插件命令

**时间**: 2025/7/21 19:38:44

**消息**: 开始执行测试

**详细信息**:

```json
{
  "testNames": [
    "search-test"
  ],
  "testCount": 1
}
```

---


## 🔍 DEBUG - 插件命令

**时间**: 2025/7/21 19:38:44

**消息**: 开始执行单个测试

**详细信息**:

```json
{
  "testFile": "/Users/<USER>/projects/auto-test/test-docs/search-test.yaml",
  "testName": "search-test"
}
```

---


## ℹ️ INFO - AI服务

**时间**: 2025/7/21 19:38:44

**消息**: AI服务已配置: https://api-inference.modelscope.cn/v1, 模型: Qwen/Qwen3-32B

---


## ℹ️ INFO - 测试执行器

**时间**: 2025/7/21 19:38:44

**消息**: AI服务配置成功

**详细信息**:

```json
{
  "apiUrl": "https://api-inference.modelscope.cn/v1",
  "model": "Qwen/Qwen3-32B"
}
```

---


## ℹ️ INFO - 测试执行器

**时间**: 2025/7/21 19:38:44

**消息**: 开始初始化MCP连接

**详细信息**:

```json
{
  "hasCustomConfig": false,
  "currentConnectionStatus": true
}
```

---


## 🔍 DEBUG - 测试执行器

**时间**: 2025/7/21 19:38:44

**消息**: MCP已连接，跳过初始化

---


## ℹ️ INFO - 测试执行器

**时间**: 2025/7/21 19:38:44

**消息**: 获取到AI SDK工具

**详细信息**:

```json
{
  "toolsCount": 27,
  "toolNames": [
    "browser_close",
    "browser_resize",
    "browser_console_messages",
    "browser_handle_dialog",
    "browser_evaluate",
    "browser_file_upload",
    "browser_install",
    "browser_press_key",
    "browser_type",
    "browser_navigate"
  ]
}
```

---


## ℹ️ INFO - 测试执行器

**时间**: 2025/7/21 19:38:44

**消息**: 发送AI请求

**详细信息**:

```json
{
  "testName": "search-test",
  "stepsCount": 4,
  "promptLength": 388,
  "systemPromptLength": 608
}
```

---


## ℹ️ INFO - AI服务

**时间**: 2025/7/21 19:38:44

**消息**: 发送带工具的AI流式请求

**详细信息**:

```json
{
  "model": "Qwen/Qwen3-32B",
  "messagesCount": 2,
  "toolsCount": 27,
  "contentLength": 388,
  "apiUrl": "https://api-inference.modelscope.cn/v1"
}
```

---


## ℹ️ INFO - 插件关闭

**时间**: 2025/7/21 19:39:02

**消息**: AI 测试插件已关闭

**详细信息**:

```json
{
  "timestamp": "2025-07-21T11:39:02.947Z",
  "uptime": 134.232947
}
```

---


## ℹ️ INFO - 插件启动

**时间**: 2025/7/21 19:39:07

**消息**: AI 测试插件已启动

**详细信息**:

```json
{
  "version": "0.0.1",
  "timestamp": "2025-07-21T11:39:07.842Z",
  "config": {
    "extensionPath": "/Users/<USER>/.cursor/extensions/ai-test.ai-test-0.0.1",
    "workspaceRoot": "/Users/<USER>/projects/auto-test",
    "nodeVersion": "v20.19.0",
    "platform": "darwin"
  },
  "workspaceRoot": "/Users/<USER>/projects/auto-test",
  "logFilePath": "/Users/<USER>/projects/auto-test/error.md"
}
```

---


## ℹ️ INFO - 插件关闭

**时间**: 2025/7/21 19:39:10

**消息**: AI 测试插件已关闭

**详细信息**:

```json
{
  "timestamp": "2025-07-21T11:39:10.377Z",
  "uptime": 7.266005167
}
```

---


## ℹ️ INFO - 插件启动

**时间**: 2025/7/21 19:39:13

**消息**: AI 测试插件已启动

**详细信息**:

```json
{
  "version": "0.0.1",
  "timestamp": "2025-07-21T11:39:13.427Z",
  "config": {
    "extensionPath": "/Users/<USER>/.cursor/extensions/ai-test.ai-test-0.0.1",
    "workspaceRoot": "/Users/<USER>/projects/auto-test",
    "nodeVersion": "v20.19.0",
    "platform": "darwin"
  },
  "workspaceRoot": "/Users/<USER>/projects/auto-test",
  "logFilePath": "/Users/<USER>/projects/auto-test/error.md"
}
```

---


## ℹ️ INFO - 插件命令

**时间**: 2025/7/21 19:39:17

**消息**: 开始执行测试

**详细信息**:

```json
{
  "testNames": [
    "search-test"
  ],
  "testCount": 1
}
```

---


## 🔍 DEBUG - 插件命令

**时间**: 2025/7/21 19:39:17

**消息**: 开始执行单个测试

**详细信息**:

```json
{
  "testFile": "/Users/<USER>/projects/auto-test/test-docs/search-test.yaml",
  "testName": "search-test"
}
```

---


## ℹ️ INFO - AI服务

**时间**: 2025/7/21 19:39:17

**消息**: AI服务已配置: https://api-inference.modelscope.cn/v1, 模型: Qwen/Qwen3-32B

---


## ℹ️ INFO - 测试执行器

**时间**: 2025/7/21 19:39:17

**消息**: AI服务配置成功

**详细信息**:

```json
{
  "apiUrl": "https://api-inference.modelscope.cn/v1",
  "model": "Qwen/Qwen3-32B"
}
```

---


## ℹ️ INFO - 测试执行器

**时间**: 2025/7/21 19:39:17

**消息**: 开始初始化MCP连接

**详细信息**:

```json
{
  "hasCustomConfig": false,
  "currentConnectionStatus": false
}
```

---


## 🔍 DEBUG - 测试执行器

**时间**: 2025/7/21 19:39:17

**消息**: 使用MCP配置

**详细信息**:

```json
{
  "connectionType": "stdio",
  "serverCommand": "npx",
  "timeout": 30000
}
```

---


## ℹ️ INFO - MCP客户端

**时间**: 2025/7/21 19:39:17

**消息**: 开始连接MCP服务器

**详细信息**:

```json
{
  "connectionType": "stdio",
  "serverCommand": "npx",
  "serverArgs": [
    "@playwright/mcp@latest",
    "--vision"
  ]
}
```

---


## 🔍 DEBUG - MCP客户端

**时间**: 2025/7/21 19:39:17

**消息**: 使用stdio连接类型创建AI SDK MCP客户端

---


## ℹ️ INFO - MCP客户端

**时间**: 2025/7/21 19:39:17

**消息**: 正在建立AI SDK STDIO连接

**详细信息**:

```json
{
  "command": "npx",
  "args": [
    "@playwright/mcp@latest",
    "--vision"
  ]
}
```

---


## ℹ️ INFO - MCP客户端

**时间**: 2025/7/21 19:39:20

**消息**: AI SDK STDIO连接建立成功

---


## 🔍 DEBUG - MCP客户端

**时间**: 2025/7/21 19:39:20

**消息**: 正在获取可用工具列表

---


## ℹ️ INFO - MCP客户端

**时间**: 2025/7/21 19:39:20

**消息**: MCP客户端连接成功

**详细信息**:

```json
{
  "connectionType": "stdio",
  "availableToolsCount": 27,
  "tools": [
    "browser_close",
    "browser_resize",
    "browser_console_messages",
    "browser_handle_dialog",
    "browser_evaluate",
    "browser_file_upload",
    "browser_install",
    "browser_press_key",
    "browser_type",
    "browser_navigate"
  ]
}
```

---


## ℹ️ INFO - 测试执行器

**时间**: 2025/7/21 19:39:20

**消息**: MCP连接初始化成功

---


## ℹ️ INFO - 测试执行器

**时间**: 2025/7/21 19:39:20

**消息**: 获取到AI SDK工具

**详细信息**:

```json
{
  "toolsCount": 27,
  "toolNames": [
    "browser_close",
    "browser_resize",
    "browser_console_messages",
    "browser_handle_dialog",
    "browser_evaluate",
    "browser_file_upload",
    "browser_install",
    "browser_press_key",
    "browser_type",
    "browser_navigate"
  ]
}
```

---


## ℹ️ INFO - 测试执行器

**时间**: 2025/7/21 19:39:20

**消息**: 发送AI请求

**详细信息**:

```json
{
  "testName": "search-test",
  "stepsCount": 4,
  "promptLength": 388,
  "systemPromptLength": 608
}
```

---


## ℹ️ INFO - AI服务

**时间**: 2025/7/21 19:39:20

**消息**: 发送带工具的AI流式请求

**详细信息**:

```json
{
  "model": "Qwen/Qwen3-32B",
  "messagesCount": 2,
  "toolsCount": 27,
  "contentLength": 388,
  "apiUrl": "https://api-inference.modelscope.cn/v1"
}
```

---


## ℹ️ INFO - 插件关闭

**时间**: 2025/7/21 19:46:59

**消息**: AI 测试插件已关闭

**详细信息**:

```json
{
  "timestamp": "2025-07-21T11:46:59.824Z",
  "uptime": 468.24474875
}
```

---


## ℹ️ INFO - 插件启动

**时间**: 2025/7/21 19:47:47

**消息**: AI 测试插件已启动

**详细信息**:

```json
{
  "version": "0.0.1",
  "timestamp": "2025-07-21T11:47:47.876Z",
  "config": {
    "extensionPath": "/Users/<USER>/.cursor/extensions/ai-test.ai-test-0.0.1",
    "workspaceRoot": "/Users/<USER>/projects/auto-test",
    "nodeVersion": "v20.19.0",
    "platform": "darwin"
  },
  "workspaceRoot": "/Users/<USER>/projects/auto-test",
  "logFilePath": "/Users/<USER>/projects/auto-test/error.md"
}
```

---


## ℹ️ INFO - 插件关闭

**时间**: 2025/7/21 19:47:52

**消息**: AI 测试插件已关闭

**详细信息**:

```json
{
  "timestamp": "2025-07-21T11:47:52.338Z",
  "uptime": 52.215208083
}
```

---


## ℹ️ INFO - 插件启动

**时间**: 2025/7/21 19:47:55

**消息**: AI 测试插件已启动

**详细信息**:

```json
{
  "version": "0.0.1",
  "timestamp": "2025-07-21T11:47:55.168Z",
  "config": {
    "extensionPath": "/Users/<USER>/.cursor/extensions/ai-test.ai-test-0.0.1",
    "workspaceRoot": "/Users/<USER>/projects/auto-test",
    "nodeVersion": "v20.19.0",
    "platform": "darwin"
  },
  "workspaceRoot": "/Users/<USER>/projects/auto-test",
  "logFilePath": "/Users/<USER>/projects/auto-test/error.md"
}
```

---


## ℹ️ INFO - 插件命令

**时间**: 2025/7/21 19:48:01

**消息**: 开始执行测试

**详细信息**:

```json
{
  "testNames": [
    "search-test"
  ],
  "testCount": 1
}
```

---


## 🔍 DEBUG - 插件命令

**时间**: 2025/7/21 19:48:01

**消息**: 开始执行单个测试

**详细信息**:

```json
{
  "testFile": "/Users/<USER>/projects/auto-test/test-docs/search-test.yaml",
  "testName": "search-test"
}
```

---


## ℹ️ INFO - AI服务

**时间**: 2025/7/21 19:48:01

**消息**: AI服务已配置: https://api-inference.modelscope.cn/v1, 模型: Qwen/Qwen3-32B

---


## ℹ️ INFO - 测试执行器

**时间**: 2025/7/21 19:48:01

**消息**: AI服务配置成功

**详细信息**:

```json
{
  "apiUrl": "https://api-inference.modelscope.cn/v1",
  "model": "Qwen/Qwen3-32B"
}
```

---


## ℹ️ INFO - 测试执行器

**时间**: 2025/7/21 19:48:01

**消息**: 开始初始化MCP连接

**详细信息**:

```json
{
  "hasCustomConfig": false,
  "currentConnectionStatus": false
}
```

---


## 🔍 DEBUG - 测试执行器

**时间**: 2025/7/21 19:48:01

**消息**: 使用MCP配置

**详细信息**:

```json
{
  "connectionType": "stdio",
  "serverCommand": "npx",
  "timeout": 30000
}
```

---


## ℹ️ INFO - MCP客户端

**时间**: 2025/7/21 19:48:01

**消息**: 开始连接MCP服务器

**详细信息**:

```json
{
  "connectionType": "stdio",
  "serverCommand": "npx",
  "serverArgs": [
    "@playwright/mcp@latest",
    "--vision"
  ]
}
```

---


## 🔍 DEBUG - MCP客户端

**时间**: 2025/7/21 19:48:01

**消息**: 使用stdio连接类型创建AI SDK MCP客户端

---


## ℹ️ INFO - MCP客户端

**时间**: 2025/7/21 19:48:01

**消息**: 正在建立AI SDK STDIO连接

**详细信息**:

```json
{
  "command": "npx",
  "args": [
    "@playwright/mcp@latest",
    "--vision"
  ]
}
```

---


## ℹ️ INFO - MCP客户端

**时间**: 2025/7/21 19:48:04

**消息**: AI SDK STDIO连接建立成功

---


## 🔍 DEBUG - MCP客户端

**时间**: 2025/7/21 19:48:04

**消息**: 正在获取可用工具列表

---


## ℹ️ INFO - MCP客户端

**时间**: 2025/7/21 19:48:04

**消息**: MCP客户端连接成功

**详细信息**:

```json
{
  "connectionType": "stdio",
  "availableToolsCount": 27,
  "tools": [
    "browser_close",
    "browser_resize",
    "browser_console_messages",
    "browser_handle_dialog",
    "browser_evaluate",
    "browser_file_upload",
    "browser_install",
    "browser_press_key",
    "browser_type",
    "browser_navigate"
  ]
}
```

---


## ℹ️ INFO - 测试执行器

**时间**: 2025/7/21 19:48:04

**消息**: MCP连接初始化成功

---


## ℹ️ INFO - 测试执行器

**时间**: 2025/7/21 19:48:04

**消息**: 获取到AI SDK工具

**详细信息**:

```json
{
  "toolsCount": 27,
  "toolNames": [
    "browser_close",
    "browser_resize",
    "browser_console_messages",
    "browser_handle_dialog",
    "browser_evaluate",
    "browser_file_upload",
    "browser_install",
    "browser_press_key",
    "browser_type",
    "browser_navigate"
  ]
}
```

---


## ℹ️ INFO - 测试执行器

**时间**: 2025/7/21 19:48:04

**消息**: 发送AI请求

**详细信息**:

```json
{
  "testName": "search-test",
  "stepsCount": 4,
  "promptLength": 388,
  "systemPromptLength": 608
}
```

---


## ℹ️ INFO - AI服务

**时间**: 2025/7/21 19:48:04

**消息**: 发送带工具的AI流式请求

**详细信息**:

```json
{
  "model": "Qwen/Qwen3-32B",
  "messagesCount": 2,
  "toolsCount": 27,
  "contentLength": 388,
  "apiUrl": "https://api-inference.modelscope.cn/v1"
}
```

---


## ℹ️ INFO - 插件关闭

**时间**: 2025/7/21 19:52:25

**消息**: AI 测试插件已关闭

**详细信息**:

```json
{
  "timestamp": "2025-07-21T11:52:25.348Z",
  "uptime": 271.756225958
}
```

---


## ℹ️ INFO - 插件启动

**时间**: 2025/7/21 19:52:30

**消息**: AI 测试插件已启动

**详细信息**:

```json
{
  "version": "0.0.1",
  "timestamp": "2025-07-21T11:52:30.277Z",
  "config": {
    "extensionPath": "/Users/<USER>/.cursor/extensions/ai-test.ai-test-0.0.1",
    "workspaceRoot": "/Users/<USER>/projects/auto-test",
    "nodeVersion": "v20.19.0",
    "platform": "darwin"
  },
  "workspaceRoot": "/Users/<USER>/projects/auto-test",
  "logFilePath": "/Users/<USER>/projects/auto-test/error.md"
}
```

---


## ℹ️ INFO - 插件关闭

**时间**: 2025/7/21 19:52:35

**消息**: AI 测试插件已关闭

**详细信息**:

```json
{
  "timestamp": "2025-07-21T11:52:35.107Z",
  "uptime": 9.525952333
}
```

---


## ℹ️ INFO - 插件启动

**时间**: 2025/7/21 19:52:41

**消息**: AI 测试插件已启动

**详细信息**:

```json
{
  "version": "0.0.1",
  "timestamp": "2025-07-21T11:52:41.944Z",
  "config": {
    "extensionPath": "/Users/<USER>/.cursor/extensions/ai-test.ai-test-0.0.1",
    "workspaceRoot": "/Users/<USER>/projects/auto-test",
    "nodeVersion": "v20.19.0",
    "platform": "darwin"
  },
  "workspaceRoot": "/Users/<USER>/projects/auto-test",
  "logFilePath": "/Users/<USER>/projects/auto-test/error.md"
}
```

---


## ℹ️ INFO - 插件命令

**时间**: 2025/7/21 19:52:46

**消息**: 开始执行测试

**详细信息**:

```json
{
  "testNames": [
    "search-test"
  ],
  "testCount": 1
}
```

---


## 🔍 DEBUG - 插件命令

**时间**: 2025/7/21 19:52:46

**消息**: 开始执行单个测试

**详细信息**:

```json
{
  "testFile": "/Users/<USER>/projects/auto-test/test-docs/search-test.yaml",
  "testName": "search-test"
}
```

---


## ℹ️ INFO - AI服务

**时间**: 2025/7/21 19:52:46

**消息**: AI服务已配置: https://api-inference.modelscope.cn/v1, 模型: Qwen/Qwen3-32B

---


## ℹ️ INFO - 测试执行器

**时间**: 2025/7/21 19:52:46

**消息**: AI服务配置成功

**详细信息**:

```json
{
  "apiUrl": "https://api-inference.modelscope.cn/v1",
  "model": "Qwen/Qwen3-32B"
}
```

---


## ℹ️ INFO - 测试执行器

**时间**: 2025/7/21 19:52:46

**消息**: 开始初始化MCP连接

**详细信息**:

```json
{
  "hasCustomConfig": false,
  "currentConnectionStatus": false
}
```

---


## 🔍 DEBUG - 测试执行器

**时间**: 2025/7/21 19:52:46

**消息**: 使用MCP配置

**详细信息**:

```json
{
  "connectionType": "stdio",
  "serverCommand": "npx",
  "timeout": 30000
}
```

---


## ℹ️ INFO - MCP客户端

**时间**: 2025/7/21 19:52:46

**消息**: 开始连接MCP服务器

**详细信息**:

```json
{
  "connectionType": "stdio",
  "serverCommand": "npx",
  "serverArgs": [
    "@playwright/mcp@latest",
    "--vision"
  ]
}
```

---


## 🔍 DEBUG - MCP客户端

**时间**: 2025/7/21 19:52:46

**消息**: 使用stdio连接类型创建AI SDK MCP客户端

---


## ℹ️ INFO - MCP客户端

**时间**: 2025/7/21 19:52:46

**消息**: 正在建立AI SDK STDIO连接

**详细信息**:

```json
{
  "command": "npx",
  "args": [
    "@playwright/mcp@latest",
    "--vision"
  ]
}
```

---


## ℹ️ INFO - MCP客户端

**时间**: 2025/7/21 19:52:49

**消息**: AI SDK STDIO连接建立成功

---


## 🔍 DEBUG - MCP客户端

**时间**: 2025/7/21 19:52:49

**消息**: 正在获取可用工具列表

---


## ℹ️ INFO - MCP客户端

**时间**: 2025/7/21 19:52:49

**消息**: MCP客户端连接成功

**详细信息**:

```json
{
  "connectionType": "stdio",
  "availableToolsCount": 27,
  "tools": [
    "browser_close",
    "browser_resize",
    "browser_console_messages",
    "browser_handle_dialog",
    "browser_evaluate",
    "browser_file_upload",
    "browser_install",
    "browser_press_key",
    "browser_type",
    "browser_navigate"
  ]
}
```

---


## ℹ️ INFO - 测试执行器

**时间**: 2025/7/21 19:52:49

**消息**: MCP连接初始化成功

---


## ℹ️ INFO - 测试执行器

**时间**: 2025/7/21 19:52:49

**消息**: 获取到AI SDK工具

**详细信息**:

```json
{
  "toolsCount": 27,
  "toolNames": [
    "browser_close",
    "browser_resize",
    "browser_console_messages",
    "browser_handle_dialog",
    "browser_evaluate",
    "browser_file_upload",
    "browser_install",
    "browser_press_key",
    "browser_type",
    "browser_navigate"
  ]
}
```

---


## ℹ️ INFO - 测试执行器

**时间**: 2025/7/21 19:52:49

**消息**: 发送AI请求

**详细信息**:

```json
{
  "testName": "search-test",
  "stepsCount": 4,
  "promptLength": 388,
  "systemPromptLength": 608
}
```

---


## ℹ️ INFO - AI服务

**时间**: 2025/7/21 19:52:49

**消息**: 发送带工具的AI流式请求

**详细信息**:

```json
{
  "model": "Qwen/Qwen3-32B",
  "messagesCount": 2,
  "toolsCount": 27,
  "contentLength": 388,
  "apiUrl": "https://api-inference.modelscope.cn/v1"
}
```

---


## ℹ️ INFO - 插件关闭

**时间**: 2025/7/21 19:54:41

**消息**: AI 测试插件已关闭

**详细信息**:

```json
{
  "timestamp": "2025-07-21T11:54:41.405Z",
  "uptime": 125.005933375
}
```

---


## ℹ️ INFO - 插件启动

**时间**: 2025/7/21 19:54:48

**消息**: AI 测试插件已启动

**详细信息**:

```json
{
  "version": "0.0.1",
  "timestamp": "2025-07-21T11:54:48.289Z",
  "config": {
    "extensionPath": "/Users/<USER>/.cursor/extensions/ai-test.ai-test-0.0.1",
    "workspaceRoot": "/Users/<USER>/projects/auto-test",
    "nodeVersion": "v20.19.0",
    "platform": "darwin"
  },
  "workspaceRoot": "/Users/<USER>/projects/auto-test",
  "logFilePath": "/Users/<USER>/projects/auto-test/error.md"
}
```

---


## ℹ️ INFO - 插件关闭

**时间**: 2025/7/21 19:54:53

**消息**: AI 测试插件已关闭

**详细信息**:

```json
{
  "timestamp": "2025-07-21T11:54:53.101Z",
  "uptime": 11.490805042
}
```

---


## ℹ️ INFO - 插件启动

**时间**: 2025/7/21 19:54:56

**消息**: AI 测试插件已启动

**详细信息**:

```json
{
  "version": "0.0.1",
  "timestamp": "2025-07-21T11:54:56.075Z",
  "config": {
    "extensionPath": "/Users/<USER>/.cursor/extensions/ai-test.ai-test-0.0.1",
    "workspaceRoot": "/Users/<USER>/projects/auto-test",
    "nodeVersion": "v20.19.0",
    "platform": "darwin"
  },
  "workspaceRoot": "/Users/<USER>/projects/auto-test",
  "logFilePath": "/Users/<USER>/projects/auto-test/error.md"
}
```

---


## ℹ️ INFO - 插件命令

**时间**: 2025/7/21 19:55:00

**消息**: 开始执行测试

**详细信息**:

```json
{
  "testNames": [
    "search-test"
  ],
  "testCount": 1
}
```

---


## 🔍 DEBUG - 插件命令

**时间**: 2025/7/21 19:55:00

**消息**: 开始执行单个测试

**详细信息**:

```json
{
  "testFile": "/Users/<USER>/projects/auto-test/test-docs/search-test.yaml",
  "testName": "search-test"
}
```

---


## ℹ️ INFO - AI服务

**时间**: 2025/7/21 19:55:00

**消息**: AI服务已配置: https://api-inference.modelscope.cn/v1, 模型: Qwen/Qwen3-32B

---


## ℹ️ INFO - 测试执行器

**时间**: 2025/7/21 19:55:00

**消息**: AI服务配置成功

**详细信息**:

```json
{
  "apiUrl": "https://api-inference.modelscope.cn/v1",
  "model": "Qwen/Qwen3-32B"
}
```

---


## ℹ️ INFO - 测试执行器

**时间**: 2025/7/21 19:55:00

**消息**: 开始初始化MCP连接

**详细信息**:

```json
{
  "hasCustomConfig": false,
  "currentConnectionStatus": false
}
```

---


## 🔍 DEBUG - 测试执行器

**时间**: 2025/7/21 19:55:00

**消息**: 使用MCP配置

**详细信息**:

```json
{
  "connectionType": "stdio",
  "serverCommand": "npx",
  "timeout": 30000
}
```

---


## ℹ️ INFO - MCP客户端

**时间**: 2025/7/21 19:55:00

**消息**: 开始连接MCP服务器

**详细信息**:

```json
{
  "connectionType": "stdio",
  "serverCommand": "npx",
  "serverArgs": [
    "@playwright/mcp@latest",
    "--vision"
  ]
}
```

---


## 🔍 DEBUG - MCP客户端

**时间**: 2025/7/21 19:55:00

**消息**: 使用stdio连接类型创建AI SDK MCP客户端

---


## ℹ️ INFO - MCP客户端

**时间**: 2025/7/21 19:55:00

**消息**: 正在建立AI SDK STDIO连接

**详细信息**:

```json
{
  "command": "npx",
  "args": [
    "@playwright/mcp@latest",
    "--vision"
  ]
}
```

---


## ℹ️ INFO - MCP客户端

**时间**: 2025/7/21 19:55:02

**消息**: AI SDK STDIO连接建立成功

---


## 🔍 DEBUG - MCP客户端

**时间**: 2025/7/21 19:55:02

**消息**: 正在获取可用工具列表

---


## ℹ️ INFO - MCP客户端

**时间**: 2025/7/21 19:55:02

**消息**: MCP客户端连接成功

**详细信息**:

```json
{
  "connectionType": "stdio",
  "availableToolsCount": 27,
  "tools": [
    "browser_close",
    "browser_resize",
    "browser_console_messages",
    "browser_handle_dialog",
    "browser_evaluate",
    "browser_file_upload",
    "browser_install",
    "browser_press_key",
    "browser_type",
    "browser_navigate"
  ]
}
```

---


## ℹ️ INFO - 测试执行器

**时间**: 2025/7/21 19:55:02

**消息**: MCP连接初始化成功

---


## ℹ️ INFO - 测试执行器

**时间**: 2025/7/21 19:55:02

**消息**: 获取到AI SDK工具

**详细信息**:

```json
{
  "toolsCount": 27,
  "toolNames": [
    "browser_close",
    "browser_resize",
    "browser_console_messages",
    "browser_handle_dialog",
    "browser_evaluate",
    "browser_file_upload",
    "browser_install",
    "browser_press_key",
    "browser_type",
    "browser_navigate"
  ]
}
```

---


## ℹ️ INFO - 测试执行器

**时间**: 2025/7/21 19:55:02

**消息**: 发送AI请求

**详细信息**:

```json
{
  "testName": "search-test",
  "stepsCount": 4,
  "promptLength": 374,
  "systemPromptLength": 608
}
```

---


## ℹ️ INFO - AI服务

**时间**: 2025/7/21 19:55:02

**消息**: 发送带工具的AI流式请求

**详细信息**:

```json
{
  "model": "Qwen/Qwen3-32B",
  "messagesCount": 2,
  "toolsCount": 27,
  "contentLength": 374,
  "apiUrl": "https://api-inference.modelscope.cn/v1"
}
```

---

