# 示例网站测试

## 测试目标
验证示例网站的基本功能，包括页面导航、表单填写和内容验证。

## 测试描述
这是一个简单的Web自动化测试示例，用于演示AI驱动的Playwright MCP测试执行功能。

## 测试步骤

### 1. 访问网站首页
- 打开浏览器
- 导航到 https://example.com
- 验证页面标题包含 "Example Domain"

### 2. 检查页面内容
- 验证页面包含 "Example Domain" 标题
- 验证页面包含说明文字
- 检查页面布局是否正常

### 3. 测试页面交互
- 尝试滚动页面
- 检查是否有链接可点击
- 验证页面响应正常

## 预期结果
- 页面能够正常加载
- 所有内容显示正确
- 页面交互功能正常

## 测试数据
- 目标URL: https://example.com
- 预期标题: Example Domain
- 预期内容: This domain is for use in illustrative examples
