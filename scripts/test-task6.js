/**
 * 任务6功能验证脚本
 * 用于验证MCP客户端和测试结果生成器的基本功能
 */

const fs = require('fs');
const path = require('path');

// 模拟测试数据
const mockTestResult = {
    testName: 'example-test',
    status: 'success',
    startTime: new Date('2025-01-21T10:00:00Z'),
    endTime: new Date('2025-01-21T10:02:30Z'),
    duration: 150000, // 2.5分钟
    steps: [
        {
            stepIndex: 0,
            goal: '访问网站首页',
            operation: '导航',
            content: '打开 https://example.com',
            status: 'success',
            duration: 2000
        },
        {
            stepIndex: 1,
            goal: '验证页面标题',
            operation: '检查',
            content: '页面标题包含 "Example Domain"',
            status: 'success',
            duration: 500
        },
        {
            stepIndex: 2,
            goal: '检查页面内容',
            operation: '检查',
            content: '验证页面包含说明文字',
            status: 'success',
            duration: 300
        }
    ],
    logFile: './test-results/example-test.2025-01-21T10-00-00.log.md'
};

const mockFailedTestResult = {
    testName: 'failed-test',
    status: 'failed',
    startTime: new Date('2025-01-21T10:05:00Z'),
    endTime: new Date('2025-01-21T10:06:00Z'),
    duration: 60000,
    steps: [
        {
            stepIndex: 0,
            goal: '访问不存在的页面',
            operation: '导航',
            content: '打开 https://nonexistent.example.com',
            status: 'failed',
            duration: 30000,
            error: '页面无法访问: ERR_NAME_NOT_RESOLVED'
        }
    ],
    error: '测试执行失败: 无法访问目标页面',
    logFile: './test-results/failed-test.2025-01-21T10-05-00.log.md'
};

/**
 * 验证YAML步骤文档解析
 */
function testYamlParsing() {
    console.log('🧪 测试YAML步骤文档解析...');
    
    const yamlContent = `
name: "示例测试"
description: "验证网站基本功能"
target: "https://example.com"
steps:
  - goal: "访问网站首页"
    operation: "导航"
    content: "打开目标网站"
  - goal: "验证页面标题"
    operation: "检查"
    content: "页面标题包含预期文字"
`;

    try {
        const yaml = require('js-yaml');
        const parsed = yaml.load(yamlContent);
        
        console.log('✅ YAML解析成功');
        console.log(`   测试名称: ${parsed.name}`);
        console.log(`   步骤数量: ${parsed.steps.length}`);
        
        // 验证必需字段
        const requiredFields = ['name', 'description', 'target', 'steps'];
        const missingFields = requiredFields.filter(field => !parsed[field]);
        
        if (missingFields.length > 0) {
            console.log(`❌ 缺少必需字段: ${missingFields.join(', ')}`);
            return false;
        }
        
        // 验证步骤格式
        for (const step of parsed.steps) {
            const stepFields = ['goal', 'operation', 'content'];
            const missingStepFields = stepFields.filter(field => !step[field]);
            
            if (missingStepFields.length > 0) {
                console.log(`❌ 步骤缺少必需字段: ${missingStepFields.join(', ')}`);
                return false;
            }
        }
        
        console.log('✅ YAML格式验证通过');
        return true;
        
    } catch (error) {
        console.log(`❌ YAML解析失败: ${error.message}`);
        return false;
    }
}

/**
 * 验证测试结果报告生成
 */
function testReportGeneration() {
    console.log('\n📊 测试报告生成...');
    
    try {
        // 模拟生成单个测试报告
        const reportContent = generateMockReport(mockTestResult);
        
        console.log('✅ 单个测试报告生成成功');
        console.log(`   报告长度: ${reportContent.length} 字符`);
        
        // 验证报告内容
        const requiredSections = [
            '# 测试结果报告',
            '## 测试信息',
            '## 步骤执行统计',
            '## 步骤执行详情'
        ];
        
        const missingSections = requiredSections.filter(section => !reportContent.includes(section));
        
        if (missingSections.length > 0) {
            console.log(`❌ 报告缺少必需章节: ${missingSections.join(', ')}`);
            return false;
        }
        
        // 模拟生成汇总报告
        const summaryContent = generateMockSummaryReport([mockTestResult, mockFailedTestResult]);
        
        console.log('✅ 汇总报告生成成功');
        console.log(`   汇总报告长度: ${summaryContent.length} 字符`);
        
        return true;
        
    } catch (error) {
        console.log(`❌ 报告生成失败: ${error.message}`);
        return false;
    }
}

/**
 * 生成模拟测试报告
 */
function generateMockReport(result) {
    const lines = [];
    
    // 报告标题
    lines.push('# 测试结果报告');
    lines.push('');
    
    // 基本信息
    lines.push('## 测试信息');
    lines.push(`**测试名称**: ${result.testName}`);
    lines.push(`**执行状态**: ${getStatusEmoji(result.status)} ${getStatusText(result.status)}`);
    lines.push(`**开始时间**: ${result.startTime.toLocaleString()}`);
    lines.push(`**结束时间**: ${result.endTime.toLocaleString()}`);
    lines.push(`**执行时长**: ${formatDuration(result.duration)}`);
    
    if (result.error) {
        lines.push(`**错误信息**: ${result.error}`);
    }
    
    lines.push('');
    
    // 步骤统计
    const stepStats = calculateStepStatistics(result.steps);
    lines.push('## 步骤执行统计');
    lines.push(`- **总步骤数**: ${stepStats.total}`);
    lines.push(`- **成功步骤**: ${stepStats.success} (${stepStats.successRate.toFixed(1)}%)`);
    lines.push(`- **失败步骤**: ${stepStats.failed}`);
    lines.push('');
    
    // 步骤详情
    lines.push('## 步骤执行详情');
    lines.push('');
    
    for (const step of result.steps) {
        lines.push(`### 步骤 ${step.stepIndex + 1}: ${step.goal}`);
        lines.push(`**状态**: ${getStatusEmoji(step.status)} ${getStatusText(step.status)}`);
        lines.push(`**操作**: ${step.operation}`);
        lines.push(`**内容**: ${step.content}`);
        lines.push(`**耗时**: ${formatDuration(step.duration)}`);
        
        if (step.error) {
            lines.push(`**错误**: ${step.error}`);
        }
        
        lines.push('');
    }
    
    return lines.join('\n');
}

/**
 * 生成模拟汇总报告
 */
function generateMockSummaryReport(results) {
    const lines = [];
    
    lines.push('# 测试执行汇总报告');
    lines.push('');
    
    // 执行统计
    const summary = calculateTestSummary(results);
    lines.push('## 执行统计');
    lines.push(`**总测试数**: ${summary.totalTests}`);
    lines.push(`**通过测试**: ${summary.passedTests} (${summary.successRate.toFixed(1)}%)`);
    lines.push(`**失败测试**: ${summary.failedTests}`);
    lines.push(`**总执行时长**: ${formatDuration(summary.totalDuration)}`);
    lines.push('');
    
    // 测试结果概览
    lines.push('## 测试结果概览');
    lines.push('');
    lines.push('| 测试名称 | 状态 | 执行时长 | 错误信息 |');
    lines.push('|---------|------|----------|----------|');
    
    for (const result of results) {
        const status = `${getStatusEmoji(result.status)} ${getStatusText(result.status)}`;
        const duration = formatDuration(result.duration);
        const error = result.error ? result.error.substring(0, 50) + '...' : '-';
        
        lines.push(`| ${result.testName} | ${status} | ${duration} | ${error} |`);
    }
    
    return lines.join('\n');
}

// 辅助函数
function getStatusEmoji(status) {
    switch (status) {
        case 'success': return '✅';
        case 'failed': return '❌';
        case 'cancelled': return '⏹️';
        default: return '❓';
    }
}

function getStatusText(status) {
    switch (status) {
        case 'success': return '成功';
        case 'failed': return '失败';
        case 'cancelled': return '已取消';
        default: return '未知';
    }
}

function formatDuration(milliseconds) {
    if (milliseconds < 1000) {
        return `${milliseconds}ms`;
    } else if (milliseconds < 60000) {
        return `${(milliseconds / 1000).toFixed(1)}s`;
    } else {
        const minutes = Math.floor(milliseconds / 60000);
        const seconds = ((milliseconds % 60000) / 1000).toFixed(1);
        return `${minutes}m ${seconds}s`;
    }
}

function calculateStepStatistics(steps) {
    const total = steps.length;
    const success = steps.filter(step => step.status === 'success').length;
    const failed = steps.filter(step => step.status === 'failed').length;
    const successRate = total > 0 ? (success / total) * 100 : 0;
    
    return { total, success, failed, successRate };
}

function calculateTestSummary(results) {
    const totalTests = results.length;
    const passedTests = results.filter(result => result.status === 'success').length;
    const failedTests = results.filter(result => result.status === 'failed').length;
    const totalDuration = results.reduce((sum, result) => sum + result.duration, 0);
    const successRate = totalTests > 0 ? (passedTests / totalTests) * 100 : 0;
    
    return { totalTests, passedTests, failedTests, totalDuration, successRate };
}

/**
 * 主测试函数
 */
function runTests() {
    console.log('🚀 开始验证任务6实现...\n');
    
    const tests = [
        { name: 'YAML解析', fn: testYamlParsing },
        { name: '报告生成', fn: testReportGeneration }
    ];
    
    let passedTests = 0;
    
    for (const test of tests) {
        if (test.fn()) {
            passedTests++;
        }
    }
    
    console.log(`\n📋 测试结果: ${passedTests}/${tests.length} 通过`);
    
    if (passedTests === tests.length) {
        console.log('🎉 所有测试通过！任务6实现验证成功。');
    } else {
        console.log('⚠️  部分测试失败，请检查实现。');
    }
}

// 运行测试
if (require.main === module) {
    runTests();
}

module.exports = {
    runTests,
    testYamlParsing,
    testReportGeneration
};
