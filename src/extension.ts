import * as vscode from 'vscode';
import { MainPanel } from './ui/panels/mainPanel';
import { ConfigurationManager } from './managers/config';
import { DocumentScanner } from './managers/scanner';
import { TestTreeDataProvider } from './ui/treeView';
import { StepGenerator } from './managers/stepGenerator';
import { AIServiceClient } from './managers/aiService';
import { TestExecutor } from './managers/testExecutor';
import { TestProgress, TestExecutionResult } from './managers/mcpClient';

export function activate(context: vscode.ExtensionContext) {
    console.log('AI自动化测试插件已激活');
    
    // 显示激活消息
    vscode.window.showInformationMessage('AI自动化测试插件已激活！');

    // 创建树视图提供者
    const scanner = DocumentScanner.getInstance();
    const treeDataProvider = new TestTreeDataProvider(scanner);
    
    // 注册树视图
    const treeView = vscode.window.createTreeView('ai-test.testExplorer', {
        treeDataProvider: treeDataProvider,
        showCollapseAll: true
    });
    context.subscriptions.push(treeView);

    // 注册命令
    const commands = [
        // 打开主面板
        vscode.commands.registerCommand('ai-test.openPanel', () => {
            MainPanel.createOrShow(context.extensionUri);
        }),

        // 扫描测试文档
        vscode.commands.registerCommand('ai-test.scanDocuments', async () => {
            try {
                const configManager = ConfigurationManager.getInstance();
                const testDirectory = configManager.getTestDirectory();
                
                if (!testDirectory) {
                    vscode.window.showWarningMessage('请先配置测试目录');
                    return;
                }

                console.log(`开始扫描目录: ${testDirectory}`);
                
                const scanner = DocumentScanner.getInstance();
                const groups = await scanner.scanDirectory(testDirectory);
                
                // 启动文件系统监听
                if (!scanner.isWatching()) {
                    scanner.watchDirectory(testDirectory);
                    console.log('已启动文件系统监听');
                }
                
                // 手动更新树视图数据并刷新
                treeDataProvider.updateTestGroups(groups);
                treeDataProvider.refresh();
                
                vscode.window.showInformationMessage(`扫描完成，发现 ${groups.length} 个测试文档组`);
                return groups;
            } catch (error) {
                const message = error instanceof Error ? error.message : '扫描失败';
                vscode.window.showErrorMessage(`文档扫描失败: ${message}`);
                console.error('扫描错误详情:', error);
                throw error;
            }
        }),

        // 生成测试步骤
        vscode.commands.registerCommand('ai-test.generateSteps', async (testName?: string) => {
            try {
                if (!testName) {
                    vscode.window.showWarningMessage('请指定测试名称');
                    return;
                }

                // 获取测试目录
                const configManager = ConfigurationManager.getInstance();
                const testDirectory = configManager.getTestDirectory();

                if (!testDirectory) {
                    vscode.window.showWarningMessage('请先配置测试目录');
                    return;
                }

                // 查找测试文档
                const scanner = DocumentScanner.getInstance();
                const groups = scanner.getDocumentGroups();
                const testGroup = groups.find(g => g.name === testName);

                if (!testGroup || !testGroup.testDoc) {
                    vscode.window.showErrorMessage(`未找到测试文档: ${testName}`);
                    return;
                }

                // 显示进度
                await vscode.window.withProgress({
                    location: vscode.ProgressLocation.Notification,
                    title: `正在为 ${testName} 生成测试步骤...`,
                    cancellable: false
                }, async (progress) => {
                    progress.report({ increment: 0, message: '初始化AI服务...' });

                    const stepGenerator = StepGenerator.getInstance();

                    progress.report({ increment: 30, message: '分析测试文档...' });

                    const result = await stepGenerator.generateStepsForTest(testGroup.testDoc!);

                    progress.report({ increment: 100, message: '完成' });

                    if (result.success) {
                        vscode.window.showInformationMessage(`步骤文档生成成功: ${result.filePath}`);

                        // 刷新文档扫描以更新状态
                        await scanner.refreshDocumentsManually(testDirectory);
                        treeDataProvider.refresh();
                    } else {
                        vscode.window.showErrorMessage(`步骤生成失败: ${result.error}`);
                    }

                    return result;
                });
            } catch (error) {
                const message = error instanceof Error ? error.message : '生成失败';
                vscode.window.showErrorMessage(`步骤生成失败: ${message}`);
                console.error('步骤生成错误:', error);
                throw error;
            }
        }),

        // 执行测试
        vscode.commands.registerCommand('ai-test.executeTests', async (testNames?: string[]) => {
            try {
                if (!testNames || testNames.length === 0) {
                    vscode.window.showWarningMessage('请选择要执行的测试');
                    return;
                }

                const testExecutor = TestExecutor.getInstance();

                // 检查是否已有测试在执行
                if (testExecutor.isCurrentlyExecuting()) {
                    vscode.window.showWarningMessage('已有测试正在执行中，请等待完成后再试');
                    return;
                }

                // 获取测试文档组
                const scanner = DocumentScanner.getInstance();
                const allGroups = scanner.getDocumentGroups();
                const selectedGroups = allGroups.filter(group => testNames.includes(group.name));

                if (selectedGroups.length === 0) {
                    vscode.window.showErrorMessage('未找到指定的测试文档');
                    return;
                }

                // 检查是否有可执行的测试
                const executableGroups = selectedGroups.filter(group => group.stepDoc);
                if (executableGroups.length === 0) {
                    vscode.window.showWarningMessage('选中的测试都缺少步骤文档，请先生成步骤');
                    return;
                }

                // 显示进度并执行测试
                await vscode.window.withProgress({
                    location: vscode.ProgressLocation.Notification,
                    title: `正在执行 ${executableGroups.length} 个测试...`,
                    cancellable: true
                }, async (progress, token) => {
                    let currentTestIndex = 0;

                    // 设置取消处理
                    token.onCancellationRequested(() => {
                        testExecutor.cancelExecution();
                    });

                    const results = await testExecutor.executeSelectedTests(executableGroups, {
                        generateReport: true,
                        generateErrorReport: true
                    }, {
                        onProgress: (testProgress: TestProgress) => {
                            const overallProgress = ((currentTestIndex + (testProgress.currentStep / testProgress.totalSteps)) / executableGroups.length) * 100;
                            progress.report({
                                increment: 0,
                                message: `${testProgress.testName}: ${testProgress.stepDescription} (${testProgress.currentStep}/${testProgress.totalSteps})`
                            });
                        },
                        onTestComplete: (result: TestExecutionResult) => {
                            currentTestIndex++;
                            const statusIcon = result.status === 'success' ? '✅' : result.status === 'failed' ? '❌' : '⏹️';
                            vscode.window.showInformationMessage(`${statusIcon} 测试 ${result.testName} 执行${result.status === 'success' ? '成功' : '失败'}`);
                        },
                        onError: (error: Error) => {
                            vscode.window.showErrorMessage(`测试执行出错: ${error.message}`);
                        }
                    });

                    // 显示最终结果
                    const successCount = results.filter(r => r.status === 'success').length;
                    const failedCount = results.filter(r => r.status === 'failed').length;
                    const cancelledCount = results.filter(r => r.status === 'cancelled').length;

                    vscode.window.showInformationMessage(
                        `测试执行完成！成功: ${successCount}, 失败: ${failedCount}, 取消: ${cancelledCount}`
                    );

                    return { success: true, results };
                });

            } catch (error) {
                const message = error instanceof Error ? error.message : '执行失败';
                vscode.window.showErrorMessage(`测试执行失败: ${message}`);
                console.error('测试执行错误:', error);
                throw error;
            }
        }),

        // 刷新树视图
        vscode.commands.registerCommand('ai-test.refreshTests', () => {
            treeDataProvider.scanTests();
        }),

        // 调试命令：显示配置信息
        vscode.commands.registerCommand('ai-test.debugConfig', () => {
            const configManager = ConfigurationManager.getInstance();
            const testDirectory = configManager.getTestDirectory();
            const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
            
            const info = [
                `工作区文件夹: ${workspaceFolder?.uri.fsPath || '无'}`,
                `配置的测试目录: ${testDirectory || '未配置'}`,
                `目录是否存在: ${testDirectory ? require('fs').existsSync(testDirectory) : '无法检查'}`
            ].join('\n');
            
            vscode.window.showInformationMessage(info, { modal: true });
        }),

        // 为特定测试生成步骤
        vscode.commands.registerCommand('ai-test.generateStepsForTest', async (treeItem) => {
            if (treeItem && treeItem.group) {
                const testGroup = treeItem.group;
                const testName = testGroup.name;

                if (!testGroup.testDoc) {
                    vscode.window.showErrorMessage(`测试 ${testName} 缺少测试文档`);
                    return;
                }

                if (testGroup.stepDoc) {
                    const choice = await vscode.window.showWarningMessage(
                        `测试 ${testName} 已存在步骤文档，是否要重新生成？`,
                        '重新生成',
                        '取消'
                    );
                    if (choice !== '重新生成') {
                        return;
                    }
                }

                try {
                    await vscode.window.withProgress({
                        location: vscode.ProgressLocation.Notification,
                        title: `正在为 ${testName} 生成测试步骤...`,
                        cancellable: false
                    }, async (progress) => {
                        progress.report({ increment: 0, message: '初始化AI服务...' });

                        const stepGenerator = StepGenerator.getInstance();

                        progress.report({ increment: 30, message: '分析测试文档...' });

                        const result = await stepGenerator.generateStepsForTest(testGroup.testDoc!);

                        progress.report({ increment: 100, message: '完成' });

                        if (result.success) {
                            vscode.window.showInformationMessage(`步骤文档生成成功: ${result.filePath}`);

                            // 刷新文档扫描以更新状态
                            const configManager = ConfigurationManager.getInstance();
                            const testDirectory = configManager.getTestDirectory();
                            if (testDirectory) {
                                const scanner = DocumentScanner.getInstance();
                                await scanner.refreshDocumentsManually(testDirectory);
                                treeDataProvider.refresh();
                            }
                        } else {
                            vscode.window.showErrorMessage(`步骤生成失败: ${result.error}`);
                        }

                        return result;
                    });
                } catch (error) {
                    const message = error instanceof Error ? error.message : '生成失败';
                    vscode.window.showErrorMessage(`步骤生成失败: ${message}`);
                    console.error('步骤生成错误:', error);
                }
            }
        }),

        // 执行特定测试
        vscode.commands.registerCommand('ai-test.executeTest', async (treeItem) => {
            if (treeItem && treeItem.group) {
                const testName = treeItem.group.name;
                const group = treeItem.group;

                if (!group.stepDoc) {
                    vscode.window.showWarningMessage(`测试 ${testName} 缺少步骤文档，请先生成步骤`);
                    return;
                }

                const testExecutor = TestExecutor.getInstance();

                // 检查是否已有测试在执行
                if (testExecutor.isCurrentlyExecuting()) {
                    vscode.window.showWarningMessage('已有测试正在执行中，请等待完成后再试');
                    return;
                }

                try {
                    // 显示进度并执行测试
                    await vscode.window.withProgress({
                        location: vscode.ProgressLocation.Notification,
                        title: `正在执行测试: ${testName}`,
                        cancellable: true
                    }, async (progress, token) => {
                        // 设置取消处理
                        token.onCancellationRequested(() => {
                            testExecutor.cancelExecution();
                        });

                        const result = await testExecutor.executeTest(group.stepDoc!, {
                            generateReport: true,
                            generateErrorReport: true
                        }, {
                            onProgress: (testProgress: TestProgress) => {
                                const progressPercent = (testProgress.currentStep / testProgress.totalSteps) * 100;
                                progress.report({
                                    increment: 0,
                                    message: `${testProgress.stepDescription} (${testProgress.currentStep}/${testProgress.totalSteps})`
                                });
                            },
                            onError: (error: Error) => {
                                vscode.window.showErrorMessage(`测试执行出错: ${error.message}`);
                            }
                        });

                        // 显示执行结果
                        const statusIcon = result.status === 'success' ? '✅' : result.status === 'failed' ? '❌' : '⏹️';
                        const statusText = result.status === 'success' ? '成功' : result.status === 'failed' ? '失败' : '已取消';

                        vscode.window.showInformationMessage(`${statusIcon} 测试 ${testName} 执行${statusText}`);

                        // 如果有错误，询问是否查看错误报告
                        if (result.status === 'failed' && result.error) {
                            const choice = await vscode.window.showErrorMessage(
                                `测试执行失败: ${result.error}`,
                                '查看日志',
                                '查看错误报告'
                            );

                            if (choice === '查看日志' && result.logFile) {
                                const logUri = vscode.Uri.file(result.logFile);
                                await vscode.window.showTextDocument(logUri);
                            }
                        }

                        return result;
                    });

                } catch (error) {
                    const message = error instanceof Error ? error.message : '执行失败';
                    vscode.window.showErrorMessage(`测试执行失败: ${message}`);
                    console.error('测试执行错误:', error);
                }
            }
        })
    ];

    // 将所有命令添加到订阅中
    commands.forEach(command => context.subscriptions.push(command));

    // 注册配置变化监听
    const configWatcher = vscode.workspace.onDidChangeConfiguration(event => {
        if (event.affectsConfiguration('ai-test')) {
            console.log('AI测试配置已更改');
            
            // 如果测试目录配置改变，重新扫描并重新监听
            if (event.affectsConfiguration('ai-test.testDirectory')) {
                const configManager = ConfigurationManager.getInstance();
                const newTestDirectory = configManager.getTestDirectory();
                const scanner = DocumentScanner.getInstance();
                
                if (newTestDirectory) {
                    console.log(`测试目录已更改为: ${newTestDirectory}`);
                    
                    // 停止当前监听
                    scanner.dispose();
                    
                    // 重新扫描和监听
                    vscode.commands.executeCommand('ai-test.scanDocuments');
                } else {
                    console.log('测试目录已清空，停止监听');
                    scanner.dispose();
                }
            }
        }
    });
    context.subscriptions.push(configWatcher);

    // 创建状态栏项
    const statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Left, 100);
    statusBarItem.text = "$(beaker) AI测试";
    statusBarItem.tooltip = "打开AI自动化测试面板";
    statusBarItem.command = 'ai-test.openPanel';
    statusBarItem.show();
    context.subscriptions.push(statusBarItem);

    // 初始化时验证配置
    const configManager = ConfigurationManager.getInstance();
    configManager.validateConfig().then(result => {
        if (!result.isValid) {
            vscode.window.showWarningMessage(
                `AI测试配置不完整: ${result.errors.join(', ')}`,
                '打开配置'
            ).then(selection => {
                if (selection === '打开配置') {
                    MainPanel.createOrShow(context.extensionUri);
                }
            });
        }
    });

    console.log('AI自动化测试插件初始化完成');
}

export function deactivate() {
    // 清理资源
    const scanner = DocumentScanner.getInstance();
    scanner.dispose();

    // 清理测试执行器
    const testExecutor = TestExecutor.getInstance();
    testExecutor.cancelExecution();
    testExecutor.disconnectFromMCP().catch(error => {
        console.error('断开MCP连接时出错:', error);
    });

    console.log('AI自动化测试插件已停用');
}
