import * as fs from 'fs';
import * as path from 'path';
import { TestExecutionResult, StepExecutionResult } from './mcpClient';

/**
 * 测试结果报告格式配置
 */
export interface ReportConfig {
    includeScreenshots: boolean;    // 是否包含截图
    includeErrorDetails: boolean;   // 是否包含详细错误信息
    includeTimestamps: boolean;     // 是否包含时间戳
    includeStepDetails: boolean;    // 是否包含步骤详情
}

/**
 * 测试结果统计信息
 */
export interface TestResultSummary {
    totalTests: number;             // 总测试数
    passedTests: number;            // 通过测试数
    failedTests: number;            // 失败测试数
    cancelledTests: number;         // 取消测试数
    totalDuration: number;          // 总执行时长
    successRate: number;            // 成功率
}

/**
 * 测试结果生成器类
 * 负责生成Markdown格式的测试结果报告
 */
export class TestResultGenerator {
    private static instance: TestResultGenerator;
    private defaultConfig: ReportConfig = {
        includeScreenshots: true,
        includeErrorDetails: true,
        includeTimestamps: true,
        includeStepDetails: true
    };

    private constructor() {}

    /**
     * 获取单例实例
     */
    public static getInstance(): TestResultGenerator {
        if (!TestResultGenerator.instance) {
            TestResultGenerator.instance = new TestResultGenerator();
        }
        return TestResultGenerator.instance;
    }

    /**
     * 生成单个测试的结果报告
     */
    public async generateTestReport(
        result: TestExecutionResult, 
        config: Partial<ReportConfig> = {}
    ): Promise<string> {
        const reportConfig = { ...this.defaultConfig, ...config };
        const lines: string[] = [];

        // 报告标题
        lines.push(`# 测试结果报告`);
        lines.push('');

        // 基本信息
        lines.push('## 测试信息');
        lines.push(`**测试名称**: ${result.testName}`);
        lines.push(`**执行状态**: ${this.getStatusEmoji(result.status)} ${this.getStatusText(result.status)}`);
        
        if (reportConfig.includeTimestamps) {
            lines.push(`**开始时间**: ${result.startTime.toLocaleString()}`);
            lines.push(`**结束时间**: ${result.endTime.toLocaleString()}`);
        }
        
        lines.push(`**执行时长**: ${this.formatDuration(result.duration)}`);
        
        if (result.error) {
            lines.push(`**错误信息**: ${result.error}`);
        }
        
        if (result.logFile) {
            lines.push(`**日志文件**: [${path.basename(result.logFile)}](${result.logFile})`);
        }
        
        lines.push('');

        // 步骤执行统计
        const stepStats = this.calculateStepStatistics(result.steps);
        lines.push('## 步骤执行统计');
        lines.push(`- **总步骤数**: ${stepStats.total}`);
        lines.push(`- **成功步骤**: ${stepStats.success} (${stepStats.successRate.toFixed(1)}%)`);
        lines.push(`- **失败步骤**: ${stepStats.failed}`);
        lines.push(`- **跳过步骤**: ${stepStats.skipped}`);
        lines.push('');

        // 步骤详情
        if (reportConfig.includeStepDetails && result.steps.length > 0) {
            lines.push('## 步骤执行详情');
            lines.push('');

            for (const step of result.steps) {
                lines.push(`### 步骤 ${step.stepIndex + 1}: ${step.goal}`);
                lines.push(`**状态**: ${this.getStatusEmoji(step.status)} ${this.getStatusText(step.status)}`);
                lines.push(`**操作**: ${step.operation}`);
                lines.push(`**内容**: ${step.content}`);
                lines.push(`**耗时**: ${this.formatDuration(step.duration)}`);
                
                if (step.error && reportConfig.includeErrorDetails) {
                    lines.push(`**错误**: ${step.error}`);
                }
                
                if (step.screenshot && reportConfig.includeScreenshots) {
                    lines.push(`**截图**: ![步骤${step.stepIndex + 1}截图](${step.screenshot})`);
                }
                
                lines.push('');
            }
        }

        // 截图汇总
        if (reportConfig.includeScreenshots && result.screenshots && result.screenshots.length > 0) {
            lines.push('## 截图汇总');
            lines.push('');
            
            for (let i = 0; i < result.screenshots.length; i++) {
                const screenshot = result.screenshots[i];
                lines.push(`### 截图 ${i + 1}`);
                lines.push(`![截图${i + 1}](${screenshot})`);
                lines.push('');
            }
        }

        // 报告生成时间
        lines.push('---');
        lines.push(`*报告生成时间: ${new Date().toLocaleString()}*`);

        return lines.join('\n');
    }

    /**
     * 生成多个测试的汇总报告
     */
    public async generateSummaryReport(
        results: TestExecutionResult[], 
        config: Partial<ReportConfig> = {}
    ): Promise<string> {
        const reportConfig = { ...this.defaultConfig, ...config };
        const lines: string[] = [];

        // 报告标题
        lines.push(`# 测试执行汇总报告`);
        lines.push('');

        // 执行统计
        const summary = this.calculateTestSummary(results);
        lines.push('## 执行统计');
        lines.push(`**总测试数**: ${summary.totalTests}`);
        lines.push(`**通过测试**: ${summary.passedTests} (${summary.successRate.toFixed(1)}%)`);
        lines.push(`**失败测试**: ${summary.failedTests}`);
        lines.push(`**取消测试**: ${summary.cancelledTests}`);
        lines.push(`**总执行时长**: ${this.formatDuration(summary.totalDuration)}`);
        lines.push('');

        // 测试结果概览
        lines.push('## 测试结果概览');
        lines.push('');
        lines.push('| 测试名称 | 状态 | 执行时长 | 错误信息 |');
        lines.push('|---------|------|----------|----------|');
        
        for (const result of results) {
            const status = `${this.getStatusEmoji(result.status)} ${this.getStatusText(result.status)}`;
            const duration = this.formatDuration(result.duration);
            const error = result.error ? result.error.substring(0, 50) + (result.error.length > 50 ? '...' : '') : '-';
            
            lines.push(`| ${result.testName} | ${status} | ${duration} | ${error} |`);
        }
        
        lines.push('');

        // 详细结果
        if (reportConfig.includeStepDetails) {
            lines.push('## 详细测试结果');
            lines.push('');

            for (const result of results) {
                lines.push(`### ${result.testName}`);
                lines.push(`**状态**: ${this.getStatusEmoji(result.status)} ${this.getStatusText(result.status)}`);
                
                if (reportConfig.includeTimestamps) {
                    lines.push(`**执行时间**: ${result.startTime.toLocaleString()} - ${result.endTime.toLocaleString()}`);
                }
                
                lines.push(`**执行时长**: ${this.formatDuration(result.duration)}`);
                
                if (result.error && reportConfig.includeErrorDetails) {
                    lines.push(`**错误信息**: ${result.error}`);
                }
                
                if (result.logFile) {
                    lines.push(`**日志文件**: [${path.basename(result.logFile)}](${result.logFile})`);
                }

                // 步骤统计
                if (result.steps.length > 0) {
                    const stepStats = this.calculateStepStatistics(result.steps);
                    lines.push(`**步骤统计**: ${stepStats.success}/${stepStats.total} 成功 (${stepStats.successRate.toFixed(1)}%)`);
                }
                
                lines.push('');
            }
        }

        // 报告生成时间
        lines.push('---');
        lines.push(`*报告生成时间: ${new Date().toLocaleString()}*`);

        return lines.join('\n');
    }

    /**
     * 保存测试结果报告到文件
     */
    public async saveTestReport(
        result: TestExecutionResult, 
        outputPath?: string,
        config: Partial<ReportConfig> = {}
    ): Promise<string> {
        const reportContent = await this.generateTestReport(result, config);
        
        // 确定输出路径
        const finalPath = outputPath || this.generateReportPath(result.testName, 'result');
        
        // 确保目录存在
        const dir = path.dirname(finalPath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }
        
        // 写入文件
        await fs.promises.writeFile(finalPath, reportContent, 'utf8');
        
        console.log(`测试结果报告已保存到: ${finalPath}`);
        return finalPath;
    }

    /**
     * 保存汇总报告到文件
     */
    public async saveSummaryReport(
        results: TestExecutionResult[], 
        outputPath?: string,
        config: Partial<ReportConfig> = {}
    ): Promise<string> {
        const reportContent = await this.generateSummaryReport(results, config);
        
        // 确定输出路径
        const finalPath = outputPath || this.generateReportPath('summary', 'summary');
        
        // 确保目录存在
        const dir = path.dirname(finalPath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }
        
        // 写入文件
        await fs.promises.writeFile(finalPath, reportContent, 'utf8');
        
        console.log(`汇总报告已保存到: ${finalPath}`);
        return finalPath;
    }

    /**
     * 生成报告文件路径
     */
    private generateReportPath(testName: string, type: 'result' | 'summary'): string {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const fileName = type === 'summary'
            ? `test-summary.${timestamp}.md`
            : `${testName}.result.md`;

        // 确保test-results目录存在
        const testResultsDir = path.join(process.cwd(), 'test-results');
        if (!fs.existsSync(testResultsDir)) {
            fs.mkdirSync(testResultsDir, { recursive: true });
        }

        return path.join(testResultsDir, fileName);
    }

    /**
     * 获取状态对应的emoji
     */
    private getStatusEmoji(status: string): string {
        switch (status) {
            case 'success':
                return '✅';
            case 'failed':
                return '❌';
            case 'cancelled':
                return '⏹️';
            case 'skipped':
                return '⏭️';
            default:
                return '❓';
        }
    }

    /**
     * 获取状态对应的文本
     */
    private getStatusText(status: string): string {
        switch (status) {
            case 'success':
                return '成功';
            case 'failed':
                return '失败';
            case 'cancelled':
                return '已取消';
            case 'skipped':
                return '已跳过';
            default:
                return '未知';
        }
    }

    /**
     * 格式化时长
     */
    private formatDuration(milliseconds: number): string {
        if (milliseconds < 1000) {
            return `${milliseconds}ms`;
        } else if (milliseconds < 60000) {
            return `${(milliseconds / 1000).toFixed(1)}s`;
        } else {
            const minutes = Math.floor(milliseconds / 60000);
            const seconds = ((milliseconds % 60000) / 1000).toFixed(1);
            return `${minutes}m ${seconds}s`;
        }
    }

    /**
     * 计算步骤统计信息
     */
    private calculateStepStatistics(steps: StepExecutionResult[]): {
        total: number;
        success: number;
        failed: number;
        skipped: number;
        successRate: number;
    } {
        const total = steps.length;
        const success = steps.filter(step => step.status === 'success').length;
        const failed = steps.filter(step => step.status === 'failed').length;
        const skipped = steps.filter(step => step.status === 'skipped').length;
        const successRate = total > 0 ? (success / total) * 100 : 0;

        return { total, success, failed, skipped, successRate };
    }

    /**
     * 计算测试汇总统计信息
     */
    private calculateTestSummary(results: TestExecutionResult[]): TestResultSummary {
        const totalTests = results.length;
        const passedTests = results.filter(result => result.status === 'success').length;
        const failedTests = results.filter(result => result.status === 'failed').length;
        const cancelledTests = results.filter(result => result.status === 'cancelled').length;
        const totalDuration = results.reduce((sum, result) => sum + result.duration, 0);
        const successRate = totalTests > 0 ? (passedTests / totalTests) * 100 : 0;

        return {
            totalTests,
            passedTests,
            failedTests,
            cancelledTests,
            totalDuration,
            successRate
        };
    }

    /**
     * 保存错误信息到单独的错误文件
     */
    public async saveErrorReport(
        result: TestExecutionResult,
        outputPath?: string
    ): Promise<string | null> {
        if (!result.error && !result.steps.some(step => step.error)) {
            return null; // 没有错误，不需要生成错误报告
        }

        const lines: string[] = [];
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

        // 错误报告标题
        lines.push(`# 错误报告: ${result.testName}`);
        lines.push('');
        lines.push(`**生成时间**: ${new Date().toLocaleString()}`);
        lines.push(`**测试状态**: ${this.getStatusText(result.status)}`);
        lines.push('');

        // 主要错误信息
        if (result.error) {
            lines.push('## 主要错误');
            lines.push('```');
            lines.push(result.error);
            lines.push('```');
            lines.push('');
        }

        // 步骤错误详情
        const failedSteps = result.steps.filter(step => step.error);
        if (failedSteps.length > 0) {
            lines.push('## 步骤错误详情');
            lines.push('');

            for (const step of failedSteps) {
                lines.push(`### 步骤 ${step.stepIndex + 1}: ${step.goal}`);
                lines.push(`**操作**: ${step.operation}`);
                lines.push(`**内容**: ${step.content}`);
                lines.push(`**错误信息**:`);
                lines.push('```');
                lines.push(step.error || '未知错误');
                lines.push('```');
                lines.push('');
            }
        }

        // 确定输出路径
        const finalPath = outputPath || path.join(
            process.cwd(),
            'test-results',
            `${result.testName}.${timestamp}.error.md`
        );

        // 确保目录存在
        const dir = path.dirname(finalPath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }

        // 写入文件
        await fs.promises.writeFile(finalPath, lines.join('\n'), 'utf8');

        console.log(`错误报告已保存到: ${finalPath}`);
        return finalPath;
    }

    /**
     * 清理旧的报告文件
     */
    public async cleanupOldReports(daysToKeep: number = 7): Promise<void> {
        const testResultsDir = path.join(process.cwd(), 'test-results');

        if (!fs.existsSync(testResultsDir)) {
            return;
        }

        const cutoffTime = Date.now() - (daysToKeep * 24 * 60 * 60 * 1000);
        const files = await fs.promises.readdir(testResultsDir);

        for (const file of files) {
            const filePath = path.join(testResultsDir, file);
            const stats = await fs.promises.stat(filePath);

            if (stats.isFile() && stats.mtime.getTime() < cutoffTime) {
                await fs.promises.unlink(filePath);
                console.log(`已删除旧报告文件: ${file}`);
            }
        }
    }
}
