import { Client } from "@modelcontextprotocol/sdk/client/index.js";
import { StdioClientTransport } from "@modelcontextprotocol/sdk/client/stdio.js";
import * as yaml from 'js-yaml';
import * as fs from 'fs';
import * as path from 'path';
import * as vscode from 'vscode';
import { AIServiceClient } from './aiService';
import { ConfigurationManager } from './config';

/**
 * 测试步骤接口
 */
export interface TestStep {
    goal: string;           // 步骤目标描述
    operation: string;      // 操作类型：导航、点击、填写、检查、等待、截图
    content: string;        // 操作内容
}

/**
 * YAML测试步骤文档格式
 */
export interface TestStepsDocument {
    name: string;           // 测试名称
    description: string;    // 测试描述
    target: string;         // 目标网站URL
    steps: TestStep[];      // 测试步骤数组
}

/**
 * 测试执行进度信息
 */
export interface TestProgress {
    testName: string;       // 测试名称
    currentStep: number;    // 当前步骤索引
    totalSteps: number;     // 总步骤数
    stepDescription: string; // 当前步骤描述
    status: 'running' | 'completed' | 'failed' | 'cancelled'; // 执行状态
}

/**
 * 测试执行结果
 */
export interface TestExecutionResult {
    testName: string;       // 测试名称
    status: 'success' | 'failed' | 'cancelled'; // 执行状态
    startTime: Date;        // 开始时间
    endTime: Date;          // 结束时间
    duration: number;       // 执行时长（毫秒）
    steps: StepExecutionResult[]; // 步骤执行结果
    error?: string;         // 错误信息
    screenshots?: string[]; // 截图文件路径
    logFile?: string;       // 日志文件路径
}

/**
 * 步骤执行结果
 */
export interface StepExecutionResult {
    stepIndex: number;      // 步骤索引
    goal: string;           // 步骤目标
    operation: string;      // 操作类型
    content: string;        // 操作内容
    status: 'success' | 'failed' | 'skipped'; // 执行状态
    duration: number;       // 执行时长（毫秒）
    error?: string;         // 错误信息
    screenshot?: string;    // 截图文件路径
}

/**
 * MCP客户端错误类型
 */
export enum MCPErrorType {
    CONNECTION_ERROR = 'connection_error',
    PLAYWRIGHT_ERROR = 'playwright_error',
    PARSING_ERROR = 'parsing_error',
    EXECUTION_ERROR = 'execution_error',
    AI_ERROR = 'ai_error',
    FILE_ERROR = 'file_error'
}

/**
 * MCP客户端错误类
 */
export class MCPClientError extends Error {
    public readonly type: MCPErrorType;
    public readonly originalError?: any;

    constructor(message: string, type: MCPErrorType, originalError?: any) {
        super(message);
        this.name = 'MCPClientError';
        this.type = type;
        this.originalError = originalError;
    }
}

/**
 * MCP协议客户端类
 * 负责与Playwright MCP服务通信，执行自动化测试
 */
export class MCPClient {
    private static instance: MCPClient;
    private client: Client | null = null;
    private transport: StdioClientTransport | null = null;
    private isConnected: boolean = false;
    private aiService: AIServiceClient;
    private configManager: ConfigurationManager;
    private onProgressCallback?: (progress: TestProgress) => void;
    private currentExecution: {
        testName: string;
        cancelled: boolean;
        logFile: string;
    } | null = null;

    private constructor() {
        this.aiService = AIServiceClient.getInstance();
        this.configManager = ConfigurationManager.getInstance();
    }

    /**
     * 获取单例实例
     */
    public static getInstance(): MCPClient {
        if (!MCPClient.instance) {
            MCPClient.instance = new MCPClient();
        }
        return MCPClient.instance;
    }

    /**
     * 连接到Playwright MCP服务
     */
    public async connect(): Promise<void> {
        if (this.isConnected) {
            return;
        }

        try {
            // 创建stdio传输，连接到Playwright MCP服务
            this.transport = new StdioClientTransport({
                command: "npx",
                args: ["@playwright/mcp@latest", "--headless"]
            });

            // 创建MCP客户端
            this.client = new Client({
                name: "ai-test-vscode-extension",
                version: "1.0.0"
            });

            // 连接到MCP服务
            await this.client.connect(this.transport);
            this.isConnected = true;

            console.log('成功连接到Playwright MCP服务');
        } catch (error) {
            throw new MCPClientError(
                '连接Playwright MCP服务失败',
                MCPErrorType.CONNECTION_ERROR,
                error
            );
        }
    }

    /**
     * 断开MCP服务连接
     */
    public async disconnect(): Promise<void> {
        if (!this.isConnected) {
            return;
        }

        try {
            if (this.client) {
                await this.client.close();
                this.client = null;
            }

            if (this.transport) {
                this.transport = null;
            }

            this.isConnected = false;
            console.log('已断开Playwright MCP服务连接');
        } catch (error) {
            console.error('断开MCP连接时出错:', error);
        }
    }

    /**
     * 解析YAML步骤文档
     */
    public parseStepsDocument(yamlContent: string): TestStepsDocument {
        try {
            const parsed = yaml.load(yamlContent) as any;
            
            if (!parsed || typeof parsed !== 'object') {
                throw new Error('YAML文档格式无效');
            }

            if (!parsed.name || !parsed.description || !parsed.target || !Array.isArray(parsed.steps)) {
                throw new Error('YAML文档缺少必需字段：name, description, target, steps');
            }

            // 验证步骤格式
            for (const step of parsed.steps) {
                if (!step.goal || !step.operation || !step.content) {
                    throw new Error('步骤缺少必需字段：goal, operation, content');
                }
            }

            return {
                name: parsed.name,
                description: parsed.description,
                target: parsed.target,
                steps: parsed.steps
            };
        } catch (error) {
            throw new MCPClientError(
                `解析YAML步骤文档失败: ${error instanceof Error ? error.message : '未知错误'}`,
                MCPErrorType.PARSING_ERROR,
                error
            );
        }
    }

    /**
     * 设置进度回调函数
     */
    public onProgress(callback: (progress: TestProgress) => void): void {
        this.onProgressCallback = callback;
    }

    /**
     * 取消当前测试执行
     */
    public cancelExecution(): void {
        if (this.currentExecution) {
            this.currentExecution.cancelled = true;
            console.log(`取消测试执行: ${this.currentExecution.testName}`);
        }
    }

    /**
     * 检查是否已连接
     */
    public isClientConnected(): boolean {
        return this.isConnected && this.client !== null;
    }

    /**
     * 获取可用的MCP工具列表
     */
    public async getAvailableTools(): Promise<any[]> {
        if (!this.client) {
            throw new MCPClientError('MCP客户端未连接', MCPErrorType.CONNECTION_ERROR);
        }

        try {
            const tools = await this.client.listTools();
            return tools.tools || [];
        } catch (error) {
            throw new MCPClientError(
                '获取MCP工具列表失败',
                MCPErrorType.CONNECTION_ERROR,
                error
            );
        }
    }

    /**
     * 创建日志文件路径
     */
    private createLogFilePath(testName: string): string {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const logFileName = `${testName}.${timestamp}.log.md`;

        // 获取工作区根目录
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        const baseDir = workspaceFolder ? workspaceFolder.uri.fsPath : process.cwd();

        // 确保test-results目录存在
        const testResultsDir = path.join(baseDir, 'test-results');
        if (!fs.existsSync(testResultsDir)) {
            fs.mkdirSync(testResultsDir, { recursive: true });
        }

        return path.join(testResultsDir, logFileName);
    }

    /**
     * 写入日志
     */
    private async writeLog(logFile: string, content: string): Promise<void> {
        try {
            await fs.promises.appendFile(logFile, content + '\n', 'utf8');
        } catch (error) {
            console.error('写入日志文件失败:', error);
        }
    }

    /**
     * 执行单个测试
     */
    public async executeTest(stepFilePath: string): Promise<TestExecutionResult> {
        if (!this.isClientConnected()) {
            await this.connect();
        }

        const startTime = new Date();
        let testSteps: TestStepsDocument;
        let logFile: string;

        try {
            // 读取并解析YAML步骤文档
            const yamlContent = await fs.promises.readFile(stepFilePath, 'utf8');
            testSteps = this.parseStepsDocument(yamlContent);

            // 创建日志文件
            logFile = this.createLogFilePath(testSteps.name);

            // 设置当前执行状态
            this.currentExecution = {
                testName: testSteps.name,
                cancelled: false,
                logFile
            };

            await this.writeLog(logFile, `# 测试执行日志: ${testSteps.name}`);
            await this.writeLog(logFile, `**开始时间**: ${startTime.toISOString()}`);
            await this.writeLog(logFile, `**测试描述**: ${testSteps.description}`);
            await this.writeLog(logFile, `**目标URL**: ${testSteps.target}`);
            await this.writeLog(logFile, `**步骤文件**: ${stepFilePath}`);
            await this.writeLog(logFile, '');

        } catch (error) {
            throw new MCPClientError(
                `读取或解析步骤文档失败: ${error instanceof Error ? error.message : '未知错误'}`,
                MCPErrorType.FILE_ERROR,
                error
            );
        }

        // 通知开始执行
        if (this.onProgressCallback) {
            this.onProgressCallback({
                testName: testSteps.name,
                currentStep: 0,
                totalSteps: testSteps.steps.length,
                stepDescription: '开始执行测试',
                status: 'running'
            });
        }

        const stepResults: StepExecutionResult[] = [];
        let executionError: string | undefined;

        try {
            // 使用AI执行测试步骤
            const result = await this.executeTestWithAI(testSteps, logFile);
            stepResults.push(...result.stepResults);
            executionError = result.error;

        } catch (error) {
            executionError = error instanceof Error ? error.message : '未知执行错误';
            await this.writeLog(logFile, `**执行错误**: ${executionError}`);
        }

        const endTime = new Date();
        const duration = endTime.getTime() - startTime.getTime();

        // 确定最终状态
        const finalStatus: 'success' | 'failed' | 'cancelled' =
            this.currentExecution?.cancelled ? 'cancelled' :
            executionError ? 'failed' : 'success';

        // 写入执行总结
        await this.writeLog(logFile, '');
        await this.writeLog(logFile, '## 执行总结');
        await this.writeLog(logFile, `**结束时间**: ${endTime.toISOString()}`);
        await this.writeLog(logFile, `**执行时长**: ${duration}ms`);
        await this.writeLog(logFile, `**最终状态**: ${finalStatus}`);
        if (executionError) {
            await this.writeLog(logFile, `**错误信息**: ${executionError}`);
        }

        // 通知执行完成
        if (this.onProgressCallback) {
            this.onProgressCallback({
                testName: testSteps.name,
                currentStep: testSteps.steps.length,
                totalSteps: testSteps.steps.length,
                stepDescription: '测试执行完成',
                status: finalStatus === 'success' ? 'completed' : 'failed'
            });
        }

        // 清理当前执行状态
        this.currentExecution = null;

        return {
            testName: testSteps.name,
            status: finalStatus,
            startTime,
            endTime,
            duration,
            steps: stepResults,
            error: executionError,
            logFile
        };
    }

    /**
     * 使用AI执行测试步骤
     */
    private async executeTestWithAI(testSteps: TestStepsDocument, logFile: string): Promise<{
        stepResults: StepExecutionResult[];
        error?: string;
    }> {
        const stepResults: StepExecutionResult[] = [];

        try {
            // 获取可用的MCP工具
            const tools = await this.getAvailableTools();
            await this.writeLog(logFile, `## 可用的Playwright工具 (${tools.length}个)`);
            for (const tool of tools) {
                await this.writeLog(logFile, `- **${tool.name}**: ${tool.description || '无描述'}`);
            }
            await this.writeLog(logFile, '');

            // 构建专业测试人员提示词
            const systemPrompt = this.buildTesterPrompt(testSteps, tools);
            const userPrompt = this.buildUserPrompt(testSteps);

            await this.writeLog(logFile, '## AI测试执行开始');
            await this.writeLog(logFile, '**系统提示词**:');
            await this.writeLog(logFile, '```');
            await this.writeLog(logFile, systemPrompt);
            await this.writeLog(logFile, '```');
            await this.writeLog(logFile, '');
            await this.writeLog(logFile, '**用户提示词**:');
            await this.writeLog(logFile, '```');
            await this.writeLog(logFile, userPrompt);
            await this.writeLog(logFile, '```');
            await this.writeLog(logFile, '');

            // 调用AI服务执行测试
            const aiResponse = await this.aiService.generateSteps(userPrompt);

            await this.writeLog(logFile, '## AI响应');
            await this.writeLog(logFile, '```');
            await this.writeLog(logFile, aiResponse);
            await this.writeLog(logFile, '```');
            await this.writeLog(logFile, '');

            // 解析AI响应并执行MCP工具调用
            // 这里需要实现AI响应解析和工具调用逻辑
            // 由于这是一个复杂的实现，我们先返回模拟结果

            for (let i = 0; i < testSteps.steps.length; i++) {
                if (this.currentExecution?.cancelled) {
                    break;
                }

                const step = testSteps.steps[i];
                const stepStartTime = Date.now();

                // 通知步骤开始
                if (this.onProgressCallback) {
                    this.onProgressCallback({
                        testName: testSteps.name,
                        currentStep: i + 1,
                        totalSteps: testSteps.steps.length,
                        stepDescription: step.goal,
                        status: 'running'
                    });
                }

                await this.writeLog(logFile, `### 步骤 ${i + 1}: ${step.goal}`);
                await this.writeLog(logFile, `**操作**: ${step.operation}`);
                await this.writeLog(logFile, `**内容**: ${step.content}`);

                try {
                    // 这里应该调用相应的MCP工具
                    // 暂时模拟执行
                    await new Promise<void>(resolve => setTimeout(resolve, 1000));

                    const stepDuration = Date.now() - stepStartTime;

                    stepResults.push({
                        stepIndex: i,
                        goal: step.goal,
                        operation: step.operation,
                        content: step.content,
                        status: 'success',
                        duration: stepDuration
                    });

                    await this.writeLog(logFile, `**状态**: 成功`);
                    await this.writeLog(logFile, `**耗时**: ${stepDuration}ms`);
                    await this.writeLog(logFile, '');

                } catch (stepError) {
                    const stepDuration = Date.now() - stepStartTime;
                    const errorMessage = stepError instanceof Error ? stepError.message : '未知错误';

                    stepResults.push({
                        stepIndex: i,
                        goal: step.goal,
                        operation: step.operation,
                        content: step.content,
                        status: 'failed',
                        duration: stepDuration,
                        error: errorMessage
                    });

                    await this.writeLog(logFile, `**状态**: 失败`);
                    await this.writeLog(logFile, `**错误**: ${errorMessage}`);
                    await this.writeLog(logFile, `**耗时**: ${stepDuration}ms`);
                    await this.writeLog(logFile, '');

                    // 步骤失败时继续执行下一步，但记录错误
                }
            }

            return { stepResults };

        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '未知AI执行错误';
            await this.writeLog(logFile, `**AI执行错误**: ${errorMessage}`);
            return {
                stepResults,
                error: errorMessage
            };
        }
    }

    /**
     * 构建专业测试人员提示词
     */
    private buildTesterPrompt(testSteps: TestStepsDocument, tools: any[]): string {
        const toolDescriptions = tools.map(tool =>
            `- **${tool.name}**: ${tool.description || '无描述'}`
        ).join('\n');

        return `你是一个专业的自动化测试工程师，负责使用Playwright MCP工具执行Web自动化测试。

## 你的任务
根据提供的测试步骤，使用可用的Playwright MCP工具来执行自动化测试。你需要：

1. 分析每个测试步骤的目标和操作类型
2. 选择合适的Playwright MCP工具来实现每个步骤
3. 按顺序执行所有测试步骤
4. 处理可能出现的错误和异常情况
5. 提供详细的执行反馈

## 可用的Playwright MCP工具
${toolDescriptions}

## 操作类型映射
- **导航**: 使用 browser_navigate 工具访问网页
- **点击**: 使用 browser_click 工具点击元素
- **填写**: 使用 browser_type 工具输入文本
- **检查**: 使用 browser_snapshot 获取页面状态，然后验证内容
- **等待**: 使用 browser_wait_for 工具等待元素或时间
- **截图**: 使用 browser_take_screenshot 工具捕获截图

## 执行原则
1. 在执行每个步骤前，先获取页面快照了解当前状态
2. 使用准确的元素选择器和描述
3. 在关键操作后进行验证
4. 遇到错误时提供清晰的错误描述
5. 保持操作的逻辑性和连贯性

请严格按照测试步骤执行，并使用提供的MCP工具完成自动化测试。`;
    }

    /**
     * 构建用户提示词
     */
    private buildUserPrompt(testSteps: TestStepsDocument): string {
        const stepsText = testSteps.steps.map((step, index) =>
            `${index + 1}. **${step.goal}**\n   - 操作: ${step.operation}\n   - 内容: ${step.content}`
        ).join('\n\n');

        return `请执行以下自动化测试：

## 测试信息
- **测试名称**: ${testSteps.name}
- **测试描述**: ${testSteps.description}
- **目标网站**: ${testSteps.target}

## 测试步骤
${stepsText}

请使用可用的Playwright MCP工具按顺序执行上述测试步骤，并提供详细的执行过程和结果反馈。`;
    }

    /**
     * 执行多个测试
     */
    public async executeMultipleTests(stepFilePaths: string[]): Promise<TestExecutionResult[]> {
        const results: TestExecutionResult[] = [];

        for (const stepFilePath of stepFilePaths) {
            if (this.currentExecution?.cancelled) {
                break;
            }

            try {
                const result = await this.executeTest(stepFilePath);
                results.push(result);
            } catch (error) {
                // 记录失败的测试
                const errorMessage = error instanceof Error ? error.message : '未知错误';
                const testName = path.basename(stepFilePath, '.yaml');

                results.push({
                    testName,
                    status: 'failed',
                    startTime: new Date(),
                    endTime: new Date(),
                    duration: 0,
                    steps: [],
                    error: errorMessage
                });
            }
        }

        return results;
    }

    /**
     * 获取测试执行状态
     */
    public getCurrentExecution(): { testName: string; logFile: string } | null {
        return this.currentExecution ? {
            testName: this.currentExecution.testName,
            logFile: this.currentExecution.logFile
        } : null;
    }
}
