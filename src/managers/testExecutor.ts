import { MCPClient, TestExecutionResult, TestProgress } from './mcpClient';
import { TestResultGenerator, ReportConfig } from './resultGenerator';
import { DocumentScanner, TestDocumentGroup, DocumentStatus } from './scanner';
import { ConfigurationManager } from './config';
import * as path from 'path';

/**
 * 测试执行选项
 */
export interface TestExecutionOptions {
    generateReport: boolean;        // 是否生成报告
    generateErrorReport: boolean;   // 是否生成错误报告
    reportConfig?: Partial<ReportConfig>; // 报告配置
    outputDirectory?: string;       // 输出目录
}

/**
 * 测试执行事件
 */
export interface TestExecutionEvents {
    onProgress?: (progress: TestProgress) => void;
    onTestComplete?: (result: TestExecutionResult) => void;
    onAllComplete?: (results: TestExecutionResult[]) => void;
    onError?: (error: Error) => void;
}

/**
 * 测试执行器类
 * 整合MCP客户端和结果生成器，提供完整的测试执行功能
 */
export class TestExecutor {
    private static instance: TestExecutor;
    private mcpClient: MCPClient;
    private resultGenerator: TestResultGenerator;
    private documentScanner: DocumentScanner;
    private configManager: ConfigurationManager;
    private isExecuting: boolean = false;
    private currentResults: TestExecutionResult[] = [];

    private constructor() {
        this.mcpClient = MCPClient.getInstance();
        this.resultGenerator = TestResultGenerator.getInstance();
        this.documentScanner = DocumentScanner.getInstance();
        this.configManager = ConfigurationManager.getInstance();
    }

    /**
     * 获取单例实例
     */
    public static getInstance(): TestExecutor {
        if (!TestExecutor.instance) {
            TestExecutor.instance = new TestExecutor();
        }
        return TestExecutor.instance;
    }

    /**
     * 执行单个测试
     */
    public async executeTest(
        stepFilePath: string,
        options: TestExecutionOptions = { generateReport: true, generateErrorReport: true },
        events: TestExecutionEvents = {}
    ): Promise<TestExecutionResult> {
        if (this.isExecuting) {
            throw new Error('已有测试正在执行中，请等待完成后再试');
        }

        this.isExecuting = true;
        
        try {
            // 验证配置
            const validation = await this.configManager.validateConfig();
            if (!validation.isValid) {
                throw new Error(`配置验证失败: ${validation.errors.join(', ')}`);
            }

            // 设置进度回调
            if (events.onProgress) {
                this.mcpClient.onProgress(events.onProgress);
            }

            // 执行测试
            const result = await this.mcpClient.executeTest(stepFilePath);
            
            // 生成报告
            if (options.generateReport) {
                await this.generateReports(result, options);
            }

            // 触发完成事件
            if (events.onTestComplete) {
                events.onTestComplete(result);
            }

            return result;

        } catch (error) {
            if (events.onError) {
                events.onError(error instanceof Error ? error : new Error(String(error)));
            }
            throw error;
        } finally {
            this.isExecuting = false;
        }
    }

    /**
     * 执行多个测试
     */
    public async executeMultipleTests(
        stepFilePaths: string[],
        options: TestExecutionOptions = { generateReport: true, generateErrorReport: true },
        events: TestExecutionEvents = {}
    ): Promise<TestExecutionResult[]> {
        if (this.isExecuting) {
            throw new Error('已有测试正在执行中，请等待完成后再试');
        }

        this.isExecuting = true;
        this.currentResults = [];
        
        try {
            // 验证配置
            const validation = await this.configManager.validateConfig();
            if (!validation.isValid) {
                throw new Error(`配置验证失败: ${validation.errors.join(', ')}`);
            }

            // 设置进度回调
            if (events.onProgress) {
                this.mcpClient.onProgress(events.onProgress);
            }

            // 执行所有测试
            const results = await this.mcpClient.executeMultipleTests(stepFilePaths);
            this.currentResults = results;

            // 为每个测试生成报告
            if (options.generateReport) {
                for (const result of results) {
                    await this.generateReports(result, options);
                    
                    if (events.onTestComplete) {
                        events.onTestComplete(result);
                    }
                }

                // 生成汇总报告
                await this.generateSummaryReport(results, options);
            }

            // 触发全部完成事件
            if (events.onAllComplete) {
                events.onAllComplete(results);
            }

            return results;

        } catch (error) {
            if (events.onError) {
                events.onError(error instanceof Error ? error : new Error(String(error)));
            }
            throw error;
        } finally {
            this.isExecuting = false;
            this.currentResults = [];
        }
    }

    /**
     * 执行选中的测试文档组
     */
    public async executeSelectedTests(
        testGroups: TestDocumentGroup[],
        options: TestExecutionOptions = { generateReport: true, generateErrorReport: true },
        events: TestExecutionEvents = {}
    ): Promise<TestExecutionResult[]> {
        // 过滤出可执行的测试（有步骤文档的）
        const executableGroups = testGroups.filter(group => 
            group.stepDoc && (group.status === DocumentStatus.MISSING_RESULT || group.status === DocumentStatus.COMPLETE)
        );

        if (executableGroups.length === 0) {
            throw new Error('没有可执行的测试（缺少步骤文档）');
        }

        const stepFilePaths = executableGroups.map(group => group.stepDoc!);
        return await this.executeMultipleTests(stepFilePaths, options, events);
    }

    /**
     * 执行所有可用测试
     */
    public async executeAllTests(
        options: TestExecutionOptions = { generateReport: true, generateErrorReport: true },
        events: TestExecutionEvents = {}
    ): Promise<TestExecutionResult[]> {
        const testDirectory = this.configManager.getTestDirectory();
        if (!testDirectory) {
            throw new Error('测试目录未配置');
        }

        // 扫描测试文档
        const testGroups = await this.documentScanner.scanDirectory(testDirectory);
        
        return await this.executeSelectedTests(testGroups, options, events);
    }

    /**
     * 取消当前执行
     */
    public cancelExecution(): void {
        if (this.isExecuting) {
            this.mcpClient.cancelExecution();
            this.isExecuting = false;
            console.log('测试执行已取消');
        }
    }

    /**
     * 检查是否正在执行
     */
    public isCurrentlyExecuting(): boolean {
        return this.isExecuting;
    }

    /**
     * 获取当前执行状态
     */
    public getCurrentExecutionStatus(): { testName: string; logFile: string } | null {
        return this.mcpClient.getCurrentExecution();
    }

    /**
     * 获取当前执行的结果
     */
    public getCurrentResults(): TestExecutionResult[] {
        return [...this.currentResults];
    }

    /**
     * 生成测试报告
     */
    private async generateReports(
        result: TestExecutionResult,
        options: TestExecutionOptions
    ): Promise<void> {
        try {
            // 生成主要结果报告
            const reportPath = await this.resultGenerator.saveTestReport(
                result,
                options.outputDirectory ? 
                    path.join(options.outputDirectory, `${result.testName}.result.md`) : 
                    undefined,
                options.reportConfig
            );

            console.log(`测试报告已生成: ${reportPath}`);

            // 生成错误报告（如果有错误）
            if (options.generateErrorReport && (result.error || result.steps.some(step => step.error))) {
                const errorReportPath = await this.resultGenerator.saveErrorReport(
                    result,
                    options.outputDirectory ? 
                        path.join(options.outputDirectory, `${result.testName}.error.md`) : 
                        undefined
                );

                if (errorReportPath) {
                    console.log(`错误报告已生成: ${errorReportPath}`);
                }
            }

        } catch (error) {
            console.error('生成报告时出错:', error);
        }
    }

    /**
     * 生成汇总报告
     */
    private async generateSummaryReport(
        results: TestExecutionResult[],
        options: TestExecutionOptions
    ): Promise<void> {
        try {
            const summaryPath = await this.resultGenerator.saveSummaryReport(
                results,
                options.outputDirectory ? 
                    path.join(options.outputDirectory, 'test-summary.md') : 
                    undefined,
                options.reportConfig
            );

            console.log(`汇总报告已生成: ${summaryPath}`);

        } catch (error) {
            console.error('生成汇总报告时出错:', error);
        }
    }

    /**
     * 清理旧报告
     */
    public async cleanupOldReports(daysToKeep: number = 7): Promise<void> {
        try {
            await this.resultGenerator.cleanupOldReports(daysToKeep);
            console.log(`已清理 ${daysToKeep} 天前的旧报告`);
        } catch (error) {
            console.error('清理旧报告时出错:', error);
        }
    }

    /**
     * 获取MCP客户端连接状态
     */
    public async getMCPConnectionStatus(): Promise<boolean> {
        try {
            return this.mcpClient.isClientConnected();
        } catch (error) {
            return false;
        }
    }

    /**
     * 连接到MCP服务
     */
    public async connectToMCP(): Promise<void> {
        try {
            await this.mcpClient.connect();
            console.log('已连接到MCP服务');
        } catch (error) {
            console.error('连接MCP服务失败:', error);
            throw error;
        }
    }

    /**
     * 断开MCP服务连接
     */
    public async disconnectFromMCP(): Promise<void> {
        try {
            await this.mcpClient.disconnect();
            console.log('已断开MCP服务连接');
        } catch (error) {
            console.error('断开MCP服务连接失败:', error);
        }
    }
}
